#!/bin/bash

echo "🔐 Test du Système d'Authentification"
echo "====================================="

# Test des routes d'authentification
echo "1. Test des routes d'authentification..."
echo "   Route /login:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/login
echo ""

echo "   Route /register:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/register
echo ""

echo "   Route /admin (sans auth):"
curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/admin
echo ""

# Test de la base de données utilisateurs
echo "2. Test de la base de données utilisateurs..."
cd /var/www/tsikyportfolio
php artisan tinker --execute="echo 'Users count: ' . App\Models\User::count() . PHP_EOL; if(App\Models\User::count() > 0) { \$user = App\Models\User::first(); echo 'Admin user: ' . \$user->name . ' (' . \$user->email . ')' . PHP_EOL; }"

# Test des routes admin
echo "3. Test des routes admin..."
echo "   Dashboard: /admin"
echo "   Profiles: /admin/profiles"
echo "   Experiences: /admin/experiences"
echo "   Skills: /admin/skills"
echo "   Education: /admin/educations"
echo "   Certifications: /admin/certifications"

echo ""
echo "🎯 Informations de connexion:"
echo "   Email: <EMAIL>"
echo "   Mot de passe: admin123"

echo ""
echo "📋 URLs disponibles:"
echo "   - Login: http://localhost:8083/login"
echo "   - Register: http://localhost:8083/register"
echo "   - Admin Dashboard: http://localhost:8083/admin"
echo "   - Portfolio: http://localhost:8083"

echo ""
echo "✅ Système d'authentification configuré!"
echo "   - Laravel Auth installé"
echo "   - Bootstrap UI configuré"
echo "   - Utilisateur admin créé"
echo "   - Routes protégées"
echo "   - Back-office fonctionnel"
