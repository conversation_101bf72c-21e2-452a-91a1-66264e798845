<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300" width="400" height="300">
  <defs>
    <linearGradient id="cloudGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366f1;stop-opacity:1" />
    </linearGradient>
    <filter id="cloudGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="#0f172a"/>
  
  <!-- Cloud Shape -->
  <g transform="translate(200, 100)">
    <ellipse cx="0" cy="0" rx="60" ry="30" fill="url(#cloudGradient)" opacity="0.8" filter="url(#cloudGlow)">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="-30" cy="-10" rx="35" ry="25" fill="url(#cloudGradient)" opacity="0.7">
      <animate attributeName="opacity" values="0.5;0.9;0.5" dur="4s" begin="0.5s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="30" cy="-10" rx="35" ry="25" fill="url(#cloudGradient)" opacity="0.7">
      <animate attributeName="opacity" values="0.5;0.9;0.5" dur="4s" begin="1s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="0" cy="-25" rx="25" ry="20" fill="url(#cloudGradient)" opacity="0.8">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" begin="1.5s" repeatCount="indefinite"/>
    </ellipse>
  </g>
  
  <!-- Data Streams to Cloud -->
  <g opacity="0.7">
    <path d="M 50 200 Q 125 150 140 100" stroke="#06b6d4" stroke-width="2" fill="none" stroke-dasharray="5,5">
      <animate attributeName="stroke-dashoffset" values="0;-20" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M 350 200 Q 275 150 260 100" stroke="#8b5cf6" stroke-width="2" fill="none" stroke-dasharray="5,5">
      <animate attributeName="stroke-dashoffset" values="0;-20" dur="2s" begin="0.5s" repeatCount="indefinite"/>
    </path>
    <path d="M 200 250 Q 200 175 200 130" stroke="#6366f1" stroke-width="2" fill="none" stroke-dasharray="5,5">
      <animate attributeName="stroke-dashoffset" values="0;-20" dur="2s" begin="1s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Data Sources -->
  <!-- Server 1 -->
  <rect x="30" y="190" width="40" height="30" fill="#374151" stroke="#06b6d4" stroke-width="1" rx="3"/>
  <rect x="35" y="195" width="30" height="5" fill="#06b6d4" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
  </rect>
  <rect x="35" y="205" width="30" height="5" fill="#06b6d4" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
  </rect>
  <rect x="35" y="215" width="30" height="5" fill="#06b6d4" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Server 2 -->
  <rect x="330" y="190" width="40" height="30" fill="#374151" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <rect x="335" y="195" width="30" height="5" fill="#8b5cf6" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
  </rect>
  <rect x="335" y="205" width="30" height="5" fill="#8b5cf6" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
  </rect>
  <rect x="335" y="215" width="30" height="5" fill="#8b5cf6" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Database -->
  <ellipse cx="200" cy="250" rx="25" ry="10" fill="#374151" stroke="#6366f1" stroke-width="1"/>
  <ellipse cx="200" cy="245" rx="25" ry="10" fill="#374151" stroke="#6366f1" stroke-width="1"/>
  <ellipse cx="200" cy="240" rx="25" ry="10" fill="#6366f1" opacity="0.3">
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="2s" repeatCount="indefinite"/>
  </ellipse>
  
  <!-- Monitoring Dashboard -->
  <rect x="120" y="30" width="160" height="100" fill="#1e293b" stroke="#06b6d4" stroke-width="2" rx="8"/>
  <rect x="130" y="40" width="140" height="80" fill="#0f172a"/>
  
  <!-- Charts in Dashboard -->
  <!-- Line Chart -->
  <g transform="translate(140, 50)">
    <polyline points="0,30 20,25 40,20 60,15 80,10 100,5 120,10" 
              stroke="#06b6d4" stroke-width="2" fill="none">
      <animate attributeName="points" 
               values="0,30 20,25 40,20 60,15 80,10 100,5 120,10;0,25 20,20 40,15 60,10 80,5 100,10 120,15;0,30 20,25 40,20 60,15 80,10 100,5 120,10" 
               dur="3s" repeatCount="indefinite"/>
    </polyline>
  </g>
  
  <!-- Bar Chart -->
  <g transform="translate(140, 90)">
    <rect x="10" y="20" width="8" height="0" fill="#8b5cf6">
      <animate attributeName="height" values="0;20;0" dur="2s" repeatCount="indefinite"/>
    </rect>
    <rect x="25" y="15" width="8" height="0" fill="#8b5cf6">
      <animate attributeName="height" values="0;25;0" dur="2s" begin="0.2s" repeatCount="indefinite"/>
    </rect>
    <rect x="40" y="10" width="8" height="0" fill="#8b5cf6">
      <animate attributeName="height" values="0;30;0" dur="2s" begin="0.4s" repeatCount="indefinite"/>
    </rect>
    <rect x="55" y="18" width="8" height="0" fill="#8b5cf6">
      <animate attributeName="height" values="0;22;0" dur="2s" begin="0.6s" repeatCount="indefinite"/>
    </rect>
    <rect x="70" y="12" width="8" height="0" fill="#8b5cf6">
      <animate attributeName="height" values="0;28;0" dur="2s" begin="0.8s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Status Indicators -->
  <circle cx="250" cy="45" r="4" fill="#10b981">
    <animate attributeName="opacity" values="0.5;1;0.5" dur="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="265" cy="45" r="4" fill="#10b981">
    <animate attributeName="opacity" values="0.5;1;0.5" dur="1s" begin="0.2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="250" cy="115" r="4" fill="#f59e0b">
    <animate attributeName="opacity" values="0.5;1;0.5" dur="1s" begin="0.4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="265" cy="115" r="4" fill="#10b981">
    <animate attributeName="opacity" values="0.5;1;0.5" dur="1s" begin="0.6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Data Packets -->
  <circle cx="50" cy="200" r="3" fill="#06b6d4">
    <animateMotion dur="4s" repeatCount="indefinite">
      <path d="M 0,0 Q 75,-50 90,-100"/>
    </animateMotion>
    <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="350" cy="200" r="3" fill="#8b5cf6">
    <animateMotion dur="4s" repeatCount="indefinite">
      <path d="M 0,0 Q -75,-50 -90,-100"/>
    </animateMotion>
    <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="200" cy="250" r="3" fill="#6366f1">
    <animateMotion dur="4s" repeatCount="indefinite">
      <path d="M 0,0 Q 0,-75 0,-120"/>
    </animateMotion>
    <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Labels -->
  <text x="50" y="185" fill="#94a3b8" font-family="monospace" font-size="10" text-anchor="middle">Server 1</text>
  <text x="350" y="185" fill="#94a3b8" font-family="monospace" font-size="10" text-anchor="middle">Server 2</text>
  <text x="200" y="275" fill="#94a3b8" font-family="monospace" font-size="10" text-anchor="middle">Database</text>
  <text x="200" y="20" fill="#94a3b8" font-family="monospace" font-size="12" text-anchor="middle">Monitoring Dashboard</text>
</svg>
