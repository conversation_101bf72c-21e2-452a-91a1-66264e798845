<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300" width="400" height="300">
  <defs>
    <linearGradient id="systemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <filter id="systemGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="#0f172a"/>
  
  <!-- Server Rack -->
  <rect x="150" y="50" width="100" height="200" fill="#1e293b" stroke="url(#systemGradient)" stroke-width="2" rx="5"/>
  
  <!-- Server Units -->
  <rect x="160" y="70" width="80" height="25" fill="#374151" rx="3">
    <animate attributeName="fill" values="#374151;#6366f1;#374151" dur="3s" repeatCount="indefinite"/>
  </rect>
  <rect x="160" y="105" width="80" height="25" fill="#374151" rx="3">
    <animate attributeName="fill" values="#374151;#8b5cf6;#374151" dur="3s" begin="0.5s" repeatCount="indefinite"/>
  </rect>
  <rect x="160" y="140" width="80" height="25" fill="#374151" rx="3">
    <animate attributeName="fill" values="#374151;#06b6d4;#374151" dur="3s" begin="1s" repeatCount="indefinite"/>
  </rect>
  <rect x="160" y="175" width="80" height="25" fill="#374151" rx="3">
    <animate attributeName="fill" values="#374151;#6366f1;#374151" dur="3s" begin="1.5s" repeatCount="indefinite"/>
  </rect>
  <rect x="160" y="210" width="80" height="25" fill="#374151" rx="3">
    <animate attributeName="fill" values="#374151;#8b5cf6;#374151" dur="3s" begin="2s" repeatCount="indefinite"/>
  </rect>
  
  <!-- LED Indicators -->
  <circle cx="170" cy="82" r="3" fill="#10b981">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="170" cy="117" r="3" fill="#10b981">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" begin="0.2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="170" cy="152" r="3" fill="#10b981">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" begin="0.4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="170" cy="187" r="3" fill="#10b981">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" begin="0.6s" repeatCount="indefinite"/>
  </circle>
  <circle cx="170" cy="222" r="3" fill="#10b981">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" begin="0.8s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Network Cables -->
  <path d="M 250 82 Q 280 82 280 120 Q 280 140 300 140" stroke="#06b6d4" stroke-width="3" fill="none" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
  </path>
  <path d="M 250 117 Q 290 117 290 160 Q 290 180 310 180" stroke="#8b5cf6" stroke-width="3" fill="none" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </path>
  <path d="M 150 152 Q 120 152 120 120 Q 120 100 100 100" stroke="#6366f1" stroke-width="3" fill="none" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="1s" repeatCount="indefinite"/>
  </path>
  
  <!-- External Devices -->
  <!-- Router -->
  <rect x="300" y="130" width="40" height="20" fill="#374151" stroke="#06b6d4" stroke-width="1" rx="3"/>
  <circle cx="310" cy="140" r="2" fill="#06b6d4">
    <animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="140" r="2" fill="#06b6d4">
    <animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="330" cy="140" r="2" fill="#06b6d4">
    <animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Switch -->
  <rect x="300" y="170" width="40" height="20" fill="#374151" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <rect x="305" y="175" width="5" height="10" fill="#8b5cf6" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" repeatCount="indefinite"/>
  </rect>
  <rect x="315" y="175" width="5" height="10" fill="#8b5cf6" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" begin="0.2s" repeatCount="indefinite"/>
  </rect>
  <rect x="325" y="175" width="5" height="10" fill="#8b5cf6" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" begin="0.4s" repeatCount="indefinite"/>
  </rect>
  <rect x="335" y="175" width="5" height="10" fill="#8b5cf6" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" begin="0.6s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Workstation -->
  <rect x="80" y="90" width="40" height="20" fill="#374151" stroke="#6366f1" stroke-width="1" rx="3"/>
  <rect x="85" y="95" width="30" height="10" fill="#1e293b"/>
  <rect x="87" y="97" width="26" height="6" fill="#6366f1" opacity="0.5">
    <animate attributeName="opacity" values="0.2;0.8;0.2" dur="2s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Data Flow Particles -->
  <circle cx="250" cy="82" r="2" fill="#06b6d4">
    <animateMotion dur="3s" repeatCount="indefinite">
      <path d="M 0,0 Q 30,0 30,38 Q 30,58 50,58"/>
    </animateMotion>
    <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="250" cy="117" r="2" fill="#8b5cf6">
    <animateMotion dur="3.5s" repeatCount="indefinite">
      <path d="M 0,0 Q 40,0 40,43 Q 40,63 60,63"/>
    </animateMotion>
    <animate attributeName="opacity" values="0;1;0" dur="3.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- CPU Usage Indicator -->
  <g transform="translate(200, 30)">
    <text x="0" y="0" fill="#94a3b8" font-family="monospace" font-size="12">CPU</text>
    <rect x="0" y="5" width="50" height="8" fill="#374151" rx="2"/>
    <rect x="2" y="7" width="0" height="4" fill="url(#systemGradient)" rx="1">
      <animate attributeName="width" values="0;46;0" dur="4s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Memory Usage Indicator -->
  <g transform="translate(200, 260)">
    <text x="0" y="0" fill="#94a3b8" font-family="monospace" font-size="12">RAM</text>
    <rect x="0" y="5" width="50" height="8" fill="#374151" rx="2"/>
    <rect x="2" y="7" width="0" height="4" fill="url(#systemGradient)" rx="1">
      <animate attributeName="width" values="0;35;0" dur="3s" repeatCount="indefinite"/>
    </rect>
  </g>
</svg>
