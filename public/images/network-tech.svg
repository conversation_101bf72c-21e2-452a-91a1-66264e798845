<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300" width="400" height="300">
  <defs>
    <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="#0f172a"/>
  
  <!-- Network Nodes -->
  <circle cx="80" cy="80" r="20" fill="url(#networkGradient)" filter="url(#glow)">
    <animate attributeName="r" values="20;25;20" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="80" r="20" fill="url(#networkGradient)" filter="url(#glow)">
    <animate attributeName="r" values="20;25;20" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="150" r="25" fill="url(#networkGradient)" filter="url(#glow)">
    <animate attributeName="r" values="25;30;25" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="80" cy="220" r="20" fill="url(#networkGradient)" filter="url(#glow)">
    <animate attributeName="r" values="20;25;20" dur="2s" begin="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="220" r="20" fill="url(#networkGradient)" filter="url(#glow)">
    <animate attributeName="r" values="20;25;20" dur="2s" begin="0.3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Network Connections -->
  <line x1="80" y1="80" x2="200" y2="150" stroke="#06b6d4" stroke-width="2" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" repeatCount="indefinite"/>
  </line>
  <line x1="320" y1="80" x2="200" y2="150" stroke="#06b6d4" stroke-width="2" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" begin="0.5s" repeatCount="indefinite"/>
  </line>
  <line x1="200" y1="150" x2="80" y2="220" stroke="#06b6d4" stroke-width="2" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" begin="1s" repeatCount="indefinite"/>
  </line>
  <line x1="200" y1="150" x2="320" y2="220" stroke="#06b6d4" stroke-width="2" opacity="0.7">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" begin="1.5s" repeatCount="indefinite"/>
  </line>
  <line x1="80" y1="80" x2="80" y2="220" stroke="#8b5cf6" stroke-width="2" opacity="0.5">
    <animate attributeName="opacity" values="0.2;0.8;0.2" dur="4s" repeatCount="indefinite"/>
  </line>
  <line x1="320" y1="80" x2="320" y2="220" stroke="#8b5cf6" stroke-width="2" opacity="0.5">
    <animate attributeName="opacity" values="0.2;0.8;0.2" dur="4s" begin="0.7s" repeatCount="indefinite"/>
  </line>
  
  <!-- Data Packets -->
  <circle cx="80" cy="80" r="3" fill="#06b6d4">
    <animateMotion dur="2s" repeatCount="indefinite">
      <path d="M 0,0 L 120,70 L 0,140 L -120,70 Z"/>
    </animateMotion>
  </circle>
  <circle cx="320" cy="80" r="3" fill="#8b5cf6">
    <animateMotion dur="2.5s" repeatCount="indefinite">
      <path d="M 0,0 L -120,70 L 0,140 L 120,70 Z"/>
    </animateMotion>
  </circle>
  
  <!-- Icons -->
  <g transform="translate(190, 140)">
    <rect x="-10" y="-10" width="20" height="20" fill="#1e293b" rx="3"/>
    <rect x="-8" y="-6" width="16" height="2" fill="#06b6d4"/>
    <rect x="-8" y="-2" width="16" height="2" fill="#06b6d4"/>
    <rect x="-8" y="2" width="16" height="2" fill="#06b6d4"/>
    <circle cx="-6" cy="6" r="1" fill="#8b5cf6"/>
    <circle cx="0" cy="6" r="1" fill="#8b5cf6"/>
    <circle cx="6" cy="6" r="1" fill="#8b5cf6"/>
  </g>
</svg>
