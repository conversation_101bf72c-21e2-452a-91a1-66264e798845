/* Additional Modern Portfolio Styles */

/* Utility Classes */
.tracking-wider {
    letter-spacing: 0.1em;
}

.hover-opacity-100:hover {
    opacity: 1 !important;
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scroll Animations */
.animate {
    animation: fadeInUp 0.8s ease-out;
}

/* Enhanced Hover Effects */
.card:hover {
    transform: translateY(-8px);
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Progress Bar Enhancements */
.progress-bar {
    position: relative;
    overflow: visible;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -5px;
    width: 10px;
    height: 10px;
    background: inherit;
    border-radius: 50%;
    transform: translateY(-50%);
    box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.3rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .hero-avatar {
        width: 200px;
        height: 200px;
    }
    
    .hero-avatar-inner {
        font-size: 5rem;
    }
    
    .floating-element {
        display: none;
    }
    
    .stats-section {
        margin-top: -30px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bg-white {
        background-color: #1e293b !important;
        color: white;
    }
    
    .text-dark {
        color: white !important;
    }
    
    .card {
        background-color: #1e293b;
        border-color: #334155;
    }
}

/* Print Styles */
@media print {
    .navbar-modern,
    .floating-element,
    .btn,
    footer {
        display: none !important;
    }
    
    .hero-section {
        background: white !important;
        color: black !important;
        min-height: auto !important;
        padding: 2rem 0 !important;
    }
    
    .hero-title,
    .hero-subtitle {
        color: black !important;
    }
    
    section {
        page-break-inside: avoid;
        margin-bottom: 2rem;
    }
}

/* Loading Animation */
.loading {
    opacity: 0;
    animation: fadeInUp 0.8s ease-out 0.2s forwards;
}

/* Custom Scrollbar for Webkit */
.custom-scroll::-webkit-scrollbar {
    width: 6px;
}

.custom-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-scroll::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
.btn:focus,
.nav-link:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-primary-modern {
        background: #000;
        border: 2px solid #000;
    }
    
    .text-primary {
        color: #000 !important;
    }
    
    .bg-primary {
        background-color: #000 !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .floating-element {
        animation: none;
    }
    
    .hero-section::before {
        animation: none;
    }
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}
