@extends('admin.layout')

@section('title', 'Experiences Management')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Experiences</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Professional Experiences</h1>
        <p class="page-subtitle">Manage your work history and professional achievements.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.experiences.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add Experience
            </a>
        </div>
    </div>
</div>

@if($experiences->count() > 0)
    <div class="row">
        @foreach($experiences as $experience)
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-briefcase me-2 text-primary"></i>
                            {{ $experience->position }}
                        </h5>
                        <div>
                            @if($experience->current)
                                <span class="badge bg-success me-2">Current</span>
                            @endif
                            <span class="badge bg-info">{{ ucfirst($experience->employment_type) }}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h6 class="text-primary mb-2">{{ $experience->company }}</h6>
                        @if($experience->location)
                            <p class="text-muted small mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ $experience->location }}
                            </p>
                        @endif
                        <p class="text-muted small mb-3">
                            <i class="fas fa-calendar me-1"></i>{{ $experience->date_range }}
                        </p>
                        
                        <p class="mb-3">{{ Str::limit($experience->description, 150) }}</p>
                        
                        @if($experience->responsibilities && count($experience->responsibilities) > 0)
                            <div class="mb-3">
                                <strong class="small">Key Responsibilities:</strong>
                                <ul class="small mt-2">
                                    @foreach(array_slice($experience->responsibilities, 0, 3) as $responsibility)
                                        <li>{{ Str::limit($responsibility, 80) }}</li>
                                    @endforeach
                                    @if(count($experience->responsibilities) > 3)
                                        <li class="text-muted">... and {{ count($experience->responsibilities) - 3 }} more</li>
                                    @endif
                                </ul>
                            </div>
                        @endif
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-sort me-1"></i>Order: {{ $experience->order }}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.experiences.show', $experience) }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.experiences.edit', $experience) }}" class="btn btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.experiences.destroy', $experience) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger" 
                                            onclick="return confirm('Are you sure you want to delete this experience?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Summary Stats -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">{{ $experiences->count() }}</h4>
                            <small class="text-muted">Total Experiences</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">{{ $experiences->where('current', true)->count() }}</h4>
                            <small class="text-muted">Current Positions</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">{{ $experiences->where('employment_type', 'full-time')->count() }}</h4>
                            <small class="text-muted">Full-time Roles</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">{{ $experiences->where('employment_type', 'freelance')->count() }}</h4>
                            <small class="text-muted">Freelance Projects</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@else
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-briefcase fa-4x text-muted mb-4"></i>
            <h4 class="mb-3">No Experiences Found</h4>
            <p class="text-muted mb-4">Start building your professional portfolio by adding your first work experience.</p>
            <a href="{{ route('admin.experiences.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Add First Experience
            </a>
        </div>
    </div>
@endif
@endsection
