@extends('admin.layout')

@section('title', 'View Experience')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.experiences.index') }}">Experiences</a></li>
    <li class="breadcrumb-item active">{{ $experience->position }}</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">{{ $experience->position }}</h1>
        <p class="page-subtitle">{{ $experience->company }} • {{ $experience->date_range }}</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.experiences.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Experiences
            </a>
            <a href="{{ route('admin.experiences.edit', $experience) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>Edit
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        Experience Details
                    </h5>
                    <div>
                        @if($experience->current)
                            <span class="badge bg-success me-2">Current Position</span>
                        @endif
                        <span class="badge bg-info">{{ ucfirst($experience->employment_type) }}</span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Position Information</h6>
                        <div class="mb-3">
                            <strong><i class="fas fa-user-tie me-2 text-primary"></i>Position:</strong>
                            <span class="ms-2">{{ $experience->position }}</span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-building me-2 text-success"></i>Company:</strong>
                            <span class="ms-2">{{ $experience->company }}</span>
                        </div>
                        @if($experience->location)
                            <div class="mb-3">
                                <strong><i class="fas fa-map-marker-alt me-2 text-danger"></i>Location:</strong>
                                <span class="ms-2">{{ $experience->location }}</span>
                            </div>
                        @endif
                        <div class="mb-3">
                            <strong><i class="fas fa-briefcase me-2 text-warning"></i>Employment Type:</strong>
                            <span class="ms-2">{{ ucfirst(str_replace('-', ' ', $experience->employment_type)) }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Timeline</h6>
                        <div class="mb-3">
                            <strong><i class="fas fa-calendar-plus me-2 text-success"></i>Start Date:</strong>
                            <span class="ms-2">{{ $experience->start_date->format('F Y') }}</span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-calendar-minus me-2 text-danger"></i>End Date:</strong>
                            <span class="ms-2">
                                {{ $experience->current ? 'Present' : $experience->end_date?->format('F Y') }}
                            </span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-clock me-2 text-info"></i>Duration:</strong>
                            <span class="ms-2">{{ $experience->duration }}</span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-sort me-2 text-secondary"></i>Display Order:</strong>
                            <span class="ms-2">{{ $experience->order }}</span>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary mb-3">Description</h6>
                    <div class="bg-light p-3 rounded">
                        <p class="mb-0">{{ $experience->description }}</p>
                    </div>
                </div>

                @if($experience->responsibilities && count($experience->responsibilities) > 0)
                    <div class="mb-4">
                        <h6 class="text-primary mb-3">Key Responsibilities</h6>
                        <div class="bg-light p-3 rounded">
                            <ul class="mb-0">
                                @foreach($experience->responsibilities as $responsibility)
                                    <li class="mb-2">{{ $responsibility }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            </div>
            <div class="card-footer bg-transparent">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-calendar-plus me-1"></i>
                            Created: {{ $experience->created_at->format('M d, Y H:i') }}
                        </small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">
                            <i class="fas fa-calendar-edit me-1"></i>
                            Updated: {{ $experience->updated_at->format('M d, Y H:i') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Quick Stats
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3 d-inline-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-briefcase fa-2x text-primary"></i>
                    </div>
                    <h6>{{ $experience->position }}</h6>
                    <p class="text-muted small">{{ $experience->company }}</p>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-primary">{{ $experience->responsibilities ? count($experience->responsibilities) : 0 }}</h5>
                        <small class="text-muted">Responsibilities</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success">{{ $experience->current ? '1' : '0' }}</h5>
                        <small class="text-muted">Current</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.experiences.edit', $experience) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Experience
                    </a>
                    <a href="{{ route('admin.experiences.create') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Add New Experience
                    </a>
                    <form action="{{ route('admin.experiences.destroy', $experience) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100" 
                                onclick="return confirm('Are you sure you want to delete this experience? This action cannot be undone.')">
                            <i class="fas fa-trash me-2"></i>Delete Experience
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-timeline me-2"></i>
                    Career Timeline
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline-simple">
                    <div class="timeline-item {{ $experience->current ? 'current' : '' }}">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">{{ $experience->position }}</h6>
                            <p class="small text-muted mb-1">{{ $experience->company }}</p>
                            <small class="text-muted">{{ $experience->date_range }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Tips
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Keep descriptions updated with latest achievements</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Add quantifiable results when possible</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Update responsibilities as role evolves</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Mark current position for accurate timeline</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline-simple {
    position: relative;
    padding-left: 20px;
}

.timeline-simple::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -16px;
    top: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #6c757d;
}

.timeline-item.current .timeline-marker {
    background: #28a745;
    box-shadow: 0 0 0 2px #28a745;
}

.timeline-content {
    padding-left: 10px;
}
</style>
@endpush
