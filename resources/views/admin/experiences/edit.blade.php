@extends('admin.layout')

@section('title', 'Edit Experience')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.experiences.index') }}">Experiences</a></li>
    <li class="breadcrumb-item active">Edit Experience</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Edit Experience</h1>
        <p class="page-subtitle">Update your professional experience details.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.experiences.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Experiences
            </a>
            <a href="{{ route('admin.experiences.show', $experience) }}" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i>View
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    Experience Details
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.experiences.update', $experience) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">Position Title *</label>
                            <input type="text" class="form-control @error('position') is-invalid @enderror" 
                                   id="position" name="position" value="{{ old('position', $experience->position) }}" required>
                            @error('position')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="company" class="form-label">Company *</label>
                            <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                   id="company" name="company" value="{{ old('company', $experience->company) }}" required>
                            @error('company')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                   id="location" name="location" value="{{ old('location', $experience->location) }}" 
                                   placeholder="e.g., Antananarivo, Madagascar">
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="employment_type" class="form-label">Employment Type *</label>
                            <select class="form-select @error('employment_type') is-invalid @enderror" 
                                    id="employment_type" name="employment_type" required>
                                <option value="">Select Type</option>
                                <option value="full-time" {{ old('employment_type', $experience->employment_type) == 'full-time' ? 'selected' : '' }}>Full-time</option>
                                <option value="part-time" {{ old('employment_type', $experience->employment_type) == 'part-time' ? 'selected' : '' }}>Part-time</option>
                                <option value="contract" {{ old('employment_type', $experience->employment_type) == 'contract' ? 'selected' : '' }}>Contract</option>
                                <option value="freelance" {{ old('employment_type', $experience->employment_type) == 'freelance' ? 'selected' : '' }}>Freelance</option>
                                <option value="internship" {{ old('employment_type', $experience->employment_type) == 'internship' ? 'selected' : '' }}>Internship</option>
                            </select>
                            @error('employment_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="start_date" class="form-label">Start Date *</label>
                            <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                   id="start_date" name="start_date" value="{{ old('start_date', $experience->start_date?->format('Y-m-d')) }}" required>
                            @error('start_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                   id="end_date" name="end_date" value="{{ old('end_date', $experience->end_date?->format('Y-m-d')) }}"
                                   {{ $experience->current ? 'disabled' : '' }}>
                            @error('end_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="order" class="form-label">Display Order</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                   id="order" name="order" value="{{ old('order', $experience->order) }}" min="0">
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="current" name="current" 
                                   value="1" {{ old('current', $experience->current) ? 'checked' : '' }}>
                            <label class="form-check-label" for="current">
                                <strong>I currently work here</strong>
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" required 
                                  placeholder="Describe your role, achievements, and impact...">{{ old('description', $experience->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label class="form-label">Key Responsibilities</label>
                        <div id="responsibilities-container">
                            @php
                                $responsibilities = old('responsibilities', $experience->responsibilities ?? []);
                            @endphp
                            @if($responsibilities && count($responsibilities) > 0)
                                @foreach($responsibilities as $index => $responsibility)
                                    <div class="input-group mb-2 responsibility-item">
                                        <input type="text" class="form-control" name="responsibilities[]" 
                                               value="{{ $responsibility }}" placeholder="Enter a responsibility...">
                                        <button class="btn btn-outline-danger" type="button" onclick="removeResponsibility(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="input-group mb-2 responsibility-item">
                                    <input type="text" class="form-control" name="responsibilities[]" 
                                           placeholder="Enter a responsibility...">
                                    <button class="btn btn-outline-danger" type="button" onclick="removeResponsibility(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addResponsibility()">
                            <i class="fas fa-plus me-1"></i>Add Responsibility
                        </button>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Experience
                        </button>
                        <a href="{{ route('admin.experiences.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>

                <!-- Separate Delete Form -->
                <form action="{{ route('admin.experiences.destroy', $experience) }}" method="POST" class="mt-3">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger"
                            onclick="return confirm('Are you sure you want to delete this experience?')">
                        <i class="fas fa-trash me-2"></i>Delete Experience
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Experience Info
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $experience->created_at->format('M d, Y H:i') }}</small>
                </div>
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $experience->updated_at->format('M d, Y H:i') }}</small>
                </div>
                <div class="mb-3">
                    <strong>Duration:</strong><br>
                    <small class="text-muted">{{ $experience->date_range }}</small>
                </div>
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    <span class="badge bg-{{ $experience->current ? 'success' : 'secondary' }}">
                        {{ $experience->current ? 'Current Position' : 'Past Position' }}
                    </span>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Tips
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use action verbs to describe achievements</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Include quantifiable results when possible</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Focus on impact and value delivered</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Keep descriptions concise but informative</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function addResponsibility() {
    const container = document.getElementById('responsibilities-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2 responsibility-item';
    div.innerHTML = `
        <input type="text" class="form-control" name="responsibilities[]" placeholder="Enter a responsibility...">
        <button class="btn btn-outline-danger" type="button" onclick="removeResponsibility(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeResponsibility(button) {
    const container = document.getElementById('responsibilities-container');
    if (container.children.length > 1) {
        button.closest('.responsibility-item').remove();
    }
}

// Toggle end date based on current checkbox
document.getElementById('current').addEventListener('change', function() {
    const endDateInput = document.getElementById('end_date');
    if (this.checked) {
        endDateInput.disabled = true;
        endDateInput.value = '';
    } else {
        endDateInput.disabled = false;
    }
});
</script>
@endpush
