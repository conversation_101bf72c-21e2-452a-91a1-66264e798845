@extends('admin.layout')

@section('title', 'Certifications Management')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Certifications</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Professional Certifications</h1>
        <p class="page-subtitle">Manage your industry certifications and professional credentials.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.certifications.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add Certification
            </a>
        </div>
    </div>
</div>

@if($certifications->count() > 0)
    <div class="row">
        @foreach($certifications as $certification)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header text-center">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3 d-inline-flex align-items-center justify-content-center mb-2">
                            <i class="{{ $certification->icon ?? 'fas fa-certificate' }} fa-2x text-primary"></i>
                        </div>
                        <h6 class="mb-0">{{ $certification->name }}</h6>
                    </div>
                    <div class="card-body text-center">
                        <h6 class="text-primary mb-3">{{ $certification->issuing_organization }}</h6>
                        
                        @if($certification->description)
                            <p class="small text-muted mb-3">{{ Str::limit($certification->description, 100) }}</p>
                        @endif
                        
                        <div class="mb-3">
                            <span class="badge bg-success px-3 py-2 rounded-pill">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $certification->date_display }}
                            </span>
                        </div>
                        
                        @if($certification->expiration_date)
                            <div class="mb-3">
                                @php
                                    $isExpired = $certification->expiration_date < now();
                                    $isExpiringSoon = $certification->expiration_date < now()->addMonths(3);
                                @endphp
                                <span class="badge bg-{{ $isExpired ? 'danger' : ($isExpiringSoon ? 'warning' : 'info') }} px-3 py-2 rounded-pill">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ $isExpired ? 'Expired' : 'Expires' }}: {{ $certification->expiration_date->format('M Y') }}
                                </span>
                            </div>
                        @endif
                        
                        @if($certification->credential_id)
                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-id-card me-1"></i>
                                    ID: {{ $certification->credential_id }}
                                </small>
                            </div>
                        @endif
                        
                        @if($certification->credential_url)
                            <div class="mb-3">
                                <a href="{{ $certification->credential_url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>View Credential
                                </a>
                            </div>
                        @endif
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-sort me-1"></i>Order: {{ $certification->order }}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.certifications.show', $certification) }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.certifications.edit', $certification) }}" class="btn btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.certifications.destroy', $certification) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger" 
                                            onclick="return confirm('Are you sure you want to delete this certification?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Certifications by Status -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Certification Status Overview
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $active = $certifications->filter(function($cert) {
                            return !$cert->expiration_date || $cert->expiration_date > now();
                        });
                        $expired = $certifications->filter(function($cert) {
                            return $cert->expiration_date && $cert->expiration_date < now();
                        });
                        $expiringSoon = $certifications->filter(function($cert) {
                            return $cert->expiration_date && $cert->expiration_date < now()->addMonths(3) && $cert->expiration_date > now();
                        });
                        $noExpiration = $certifications->whereNull('expiration_date');
                    @endphp
                    
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-success">{{ $active->count() }}</h4>
                            <small class="text-muted">Active Certifications</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">{{ $expiringSoon->count() }}</h4>
                            <small class="text-muted">Expiring Soon</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-danger">{{ $expired->count() }}</h4>
                            <small class="text-muted">Expired</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">{{ $noExpiration->count() }}</h4>
                            <small class="text-muted">No Expiration</small>
                        </div>
                    </div>
                    
                    @if($expiringSoon->count() > 0)
                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Certifications Expiring Soon:</h6>
                            <ul class="mb-0">
                                @foreach($expiringSoon as $cert)
                                    <li>{{ $cert->name }} - Expires {{ $cert->expiration_date->format('M d, Y') }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    @if($expired->count() > 0)
                        <div class="alert alert-danger mt-3">
                            <h6><i class="fas fa-times-circle me-2"></i>Expired Certifications:</h6>
                            <ul class="mb-0">
                                @foreach($expired as $cert)
                                    <li>{{ $cert->name }} - Expired {{ $cert->expiration_date->format('M d, Y') }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Certifications by Organization -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        Certifications by Organization
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $byOrganization = $certifications->groupBy('issuing_organization');
                    @endphp
                    
                    <div class="row">
                        @foreach($byOrganization as $organization => $orgCertifications)
                            <div class="col-md-6 mb-3">
                                <div class="border rounded p-3">
                                    <h6 class="mb-2">{{ $organization }}</h6>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-primary">{{ $orgCertifications->count() }} certification(s)</span>
                                        <small class="text-muted">
                                            Latest: {{ $orgCertifications->sortByDesc('issue_date')->first()->issue_date->format('M Y') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@else
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-certificate fa-4x text-muted mb-4"></i>
            <h4 class="mb-3">No Certifications Found</h4>
            <p class="text-muted mb-4">Start showcasing your professional credentials by adding your first certification.</p>
            <a href="{{ route('admin.certifications.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Add First Certification
            </a>
        </div>
    </div>
@endif
@endsection
