@extends('admin.layout')

@section('title', 'Edit Certification')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.certifications.index') }}">Certifications</a></li>
    <li class="breadcrumb-item active">Edit Certification</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Edit Certification</h1>
        <p class="page-subtitle">Update your professional certification details.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.certifications.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Certifications
            </a>
            <a href="{{ route('admin.certifications.show', $certification) }}" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i>View
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-certificate me-2"></i>
                    Certification Details
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.certifications.update', $certification) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="name" class="form-label">Certification Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $certification->name) }}" required
                                   placeholder="e.g., AWS Certified Solutions Architect, CompTIA Security+">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="icon" class="form-label">Icon (Font Awesome)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i id="icon-preview" class="{{ old('icon', $certification->icon ?: 'fas fa-certificate') }}"></i>
                                </span>
                                <input type="text" class="form-control @error('icon') is-invalid @enderror" 
                                       id="icon" name="icon" value="{{ old('icon', $certification->icon) }}" 
                                       placeholder="fas fa-certificate"
                                       oninput="updateIconPreview(this.value)">
                            </div>
                            @error('icon')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="issuing_organization" class="form-label">Issuing Organization *</label>
                        <input type="text" class="form-control @error('issuing_organization') is-invalid @enderror" 
                               id="issuing_organization" name="issuing_organization" value="{{ old('issuing_organization', $certification->issuing_organization) }}" required
                               placeholder="e.g., Amazon Web Services, CompTIA, Microsoft">
                        @error('issuing_organization')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="issue_date" class="form-label">Issue Date *</label>
                            <input type="date" class="form-control @error('issue_date') is-invalid @enderror" 
                                   id="issue_date" name="issue_date" value="{{ old('issue_date', $certification->issue_date?->format('Y-m-d')) }}" required>
                            @error('issue_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="expiration_date" class="form-label">Expiration Date</label>
                            <input type="date" class="form-control @error('expiration_date') is-invalid @enderror" 
                                   id="expiration_date" name="expiration_date" value="{{ old('expiration_date', $certification->expiration_date?->format('Y-m-d')) }}">
                            <small class="text-muted">Leave empty if certification doesn't expire</small>
                            @error('expiration_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="order" class="form-label">Display Order</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                   id="order" name="order" value="{{ old('order', $certification->order) }}" min="0">
                            <small class="text-muted">Lower numbers appear first</small>
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="credential_id" class="form-label">Credential ID</label>
                            <input type="text" class="form-control @error('credential_id') is-invalid @enderror" 
                                   id="credential_id" name="credential_id" value="{{ old('credential_id', $certification->credential_id) }}" 
                                   placeholder="e.g., AWS-ASA-12345, CompTIA-SEC-67890">
                            @error('credential_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="credential_url" class="form-label">Credential URL</label>
                            <input type="url" class="form-control @error('credential_url') is-invalid @enderror" 
                                   id="credential_url" name="credential_url" value="{{ old('credential_url', $certification->credential_url) }}" 
                                   placeholder="https://www.credly.com/badges/...">
                            <small class="text-muted">Link to verify the certification</small>
                            @error('credential_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4"
                                  placeholder="Describe what this certification covers, skills validated, etc...">{{ old('description', $certification->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Preview -->
                    <div class="mb-4">
                        <label class="form-label">Preview</label>
                        <div class="border rounded p-3 bg-light text-center">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3 d-inline-flex align-items-center justify-content-center mb-2">
                                <i id="preview-icon" class="{{ old('icon', $certification->icon ?: 'fas fa-certificate') }} fa-2x text-primary"></i>
                            </div>
                            <h6 class="mb-1" id="preview-name">{{ old('name', $certification->name) }}</h6>
                            <p class="text-primary mb-2" id="preview-organization">{{ old('issuing_organization', $certification->issuing_organization) }}</p>
                            
                            <div class="mb-2">
                                <span class="badge bg-success px-3 py-2 rounded-pill" id="preview-date">
                                    <i class="fas fa-calendar me-1"></i>
                                    <span id="preview-date-text">{{ $certification->date_display }}</span>
                                </span>
                            </div>
                            
                            <div class="mb-2" id="preview-expiration" style="{{ $certification->expiration_date ? '' : 'display: none;' }}">
                                <span class="badge bg-info px-3 py-2 rounded-pill">
                                    <i class="fas fa-clock me-1"></i>
                                    Expires: <span id="preview-expiration-text">{{ $certification->expiration_date?->format('M Y') }}</span>
                                </span>
                            </div>
                            
                            <div class="mb-2" id="preview-credential" style="{{ $certification->credential_id ? '' : 'display: none;' }}">
                                <small class="text-muted">
                                    <i class="fas fa-id-card me-1"></i>
                                    ID: <span id="preview-credential-text">{{ $certification->credential_id }}</span>
                                </small>
                            </div>
                            
                            <div id="preview-url" style="{{ $certification->credential_url ? '' : 'display: none;' }}">
                                <a href="{{ $certification->credential_url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>View Credential
                                </a>
                            </div>
                            
                            <p class="mt-3 mb-0 small text-muted" id="preview-description" style="{{ $certification->description ? '' : 'display: none;' }}">{{ $certification->description }}</p>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Certification
                        </button>
                        <a href="{{ route('admin.certifications.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>

                <!-- Separate Delete Form -->
                <form action="{{ route('admin.certifications.destroy', $certification) }}" method="POST" class="mt-3">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger"
                            onclick="return confirm('Are you sure you want to delete this certification?')">
                        <i class="fas fa-trash me-2"></i>Delete Certification
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Certification Info
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    @php
                        $isExpired = $certification->expiration_date && $certification->expiration_date < now();
                        $isExpiringSoon = $certification->expiration_date && $certification->expiration_date < now()->addMonths(3) && $certification->expiration_date > now();
                    @endphp
                    <span class="badge bg-{{ $isExpired ? 'danger' : ($isExpiringSoon ? 'warning' : 'success') }}">
                        {{ $isExpired ? 'Expired' : ($isExpiringSoon ? 'Expiring Soon' : 'Active') }}
                    </span>
                </div>
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $certification->created_at->format('M d, Y H:i') }}</small>
                </div>
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $certification->updated_at->format('M d, Y H:i') }}</small>
                </div>
                @if($certification->credential_id)
                    <div class="mb-3">
                        <strong>Credential ID:</strong><br>
                        <code>{{ $certification->credential_id }}</code>
                    </div>
                @endif
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-icons me-2"></i>
                    Popular Icons
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fab fa-aws')">
                            <i class="fab fa-aws"></i><br><small>AWS</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fab fa-microsoft')">
                            <i class="fab fa-microsoft"></i><br><small>Microsoft</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fab fa-google')">
                            <i class="fab fa-google"></i><br><small>Google</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fas fa-shield-alt')">
                            <i class="fas fa-shield-alt"></i><br><small>Security</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fas fa-network-wired')">
                            <i class="fas fa-network-wired"></i><br><small>Network</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fas fa-certificate')">
                            <i class="fas fa-certificate"></i><br><small>General</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateIconPreview(iconClass) {
    if (iconClass) {
        document.getElementById('icon-preview').className = iconClass;
        document.getElementById('preview-icon').className = iconClass + ' fa-2x text-primary';
    }
}

function setIcon(iconClass) {
    document.getElementById('icon').value = iconClass;
    updateIconPreview(iconClass);
}

// Real-time preview updates
document.getElementById('name').addEventListener('input', function() {
    document.getElementById('preview-name').textContent = this.value || 'Certification Name';
});

document.getElementById('issuing_organization').addEventListener('input', function() {
    document.getElementById('preview-organization').textContent = this.value || 'Issuing Organization';
});

document.getElementById('credential_id').addEventListener('input', function() {
    const element = document.getElementById('preview-credential');
    const textElement = document.getElementById('preview-credential-text');
    if (this.value) {
        textElement.textContent = this.value;
        element.style.display = 'block';
    } else {
        element.style.display = 'none';
    }
});

document.getElementById('credential_url').addEventListener('input', function() {
    const element = document.getElementById('preview-url');
    if (this.value) {
        element.querySelector('a').href = this.value;
        element.style.display = 'block';
    } else {
        element.style.display = 'none';
    }
});

document.getElementById('description').addEventListener('input', function() {
    const element = document.getElementById('preview-description');
    if (this.value) {
        element.textContent = this.value;
        element.style.display = 'block';
    } else {
        element.style.display = 'none';
    }
});

function updateDatePreview() {
    const issueDate = document.getElementById('issue_date').value;
    const expirationDate = document.getElementById('expiration_date').value;
    
    if (issueDate) {
        const date = new Date(issueDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
        document.getElementById('preview-date-text').textContent = date;
    }
    
    const expirationElement = document.getElementById('preview-expiration');
    if (expirationDate) {
        const date = new Date(expirationDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
        document.getElementById('preview-expiration-text').textContent = date;
        expirationElement.style.display = 'block';
    } else {
        expirationElement.style.display = 'none';
    }
}

document.getElementById('issue_date').addEventListener('change', updateDatePreview);
document.getElementById('expiration_date').addEventListener('change', updateDatePreview);
</script>
@endpush
