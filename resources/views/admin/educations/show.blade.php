@extends('admin.layout')

@section('title', 'View Education')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.educations.index') }}">Education</a></li>
    <li class="breadcrumb-item active">{{ $education->degree }}</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">{{ $education->degree }}</h1>
        <p class="page-subtitle">{{ $education->institution }} • {{ $education->date_range }}</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.educations.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Education
            </a>
            <a href="{{ route('admin.educations.edit', $education) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>Edit
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        Education Details
                    </h5>
                    @if($education->grade)
                        <span class="badge bg-success">{{ $education->grade }}</span>
                    @endif
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Academic Information</h6>
                        <div class="mb-3">
                            <strong><i class="fas fa-graduation-cap me-2 text-primary"></i>Degree:</strong>
                            <span class="ms-2">{{ $education->degree }}</span>
                        </div>
                        @if($education->field_of_study)
                            <div class="mb-3">
                                <strong><i class="fas fa-book me-2 text-success"></i>Field of Study:</strong>
                                <span class="ms-2">{{ $education->field_of_study }}</span>
                            </div>
                        @endif
                        <div class="mb-3">
                            <strong><i class="fas fa-university me-2 text-info"></i>Institution:</strong>
                            <span class="ms-2">{{ $education->institution }}</span>
                        </div>
                        @if($education->location)
                            <div class="mb-3">
                                <strong><i class="fas fa-map-marker-alt me-2 text-danger"></i>Location:</strong>
                                <span class="ms-2">{{ $education->location }}</span>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Timeline & Performance</h6>
                        <div class="mb-3">
                            <strong><i class="fas fa-calendar-plus me-2 text-success"></i>Start Date:</strong>
                            <span class="ms-2">{{ $education->start_date->format('F Y') }}</span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-calendar-minus me-2 text-danger"></i>End Date:</strong>
                            <span class="ms-2">
                                {{ $education->end_date ? $education->end_date->format('F Y') : 'Present' }}
                            </span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-clock me-2 text-info"></i>Duration:</strong>
                            <span class="ms-2">{{ $education->duration }}</span>
                        </div>
                        @if($education->grade)
                            <div class="mb-3">
                                <strong><i class="fas fa-trophy me-2 text-warning"></i>Grade:</strong>
                                <span class="ms-2 badge bg-success">{{ $education->grade }}</span>
                            </div>
                        @endif
                        <div class="mb-3">
                            <strong><i class="fas fa-sort me-2 text-secondary"></i>Display Order:</strong>
                            <span class="ms-2">{{ $education->order }}</span>
                        </div>
                    </div>
                </div>

                @if($education->description)
                    <div class="mb-4">
                        <h6 class="text-primary mb-3">Description</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0">{{ $education->description }}</p>
                        </div>
                    </div>
                @endif
            </div>
            <div class="card-footer bg-transparent">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-calendar-plus me-1"></i>
                            Created: {{ $education->created_at->format('M d, Y H:i') }}
                        </small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">
                            <i class="fas fa-calendar-edit me-1"></i>
                            Updated: {{ $education->updated_at->format('M d, Y H:i') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Quick Overview
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3 d-inline-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-graduation-cap fa-2x text-primary"></i>
                    </div>
                    <h6>{{ $education->degree }}</h6>
                    <p class="text-muted small">{{ $education->institution }}</p>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-primary">{{ $education->start_date->format('Y') }}</h5>
                        <small class="text-muted">Start Year</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success">{{ $education->end_date ? $education->end_date->format('Y') : 'Present' }}</h5>
                        <small class="text-muted">End Year</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.educations.edit', $education) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Education
                    </a>
                    <a href="{{ route('admin.educations.create') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Add New Education
                    </a>
                    <form action="{{ route('admin.educations.destroy', $education) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100" 
                                onclick="return confirm('Are you sure you want to delete this education record? This action cannot be undone.')">
                            <i class="fas fa-trash me-2"></i>Delete Education
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Education Stats
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Type:</strong><br>
                    <span class="badge bg-info">
                        @if(str_contains(strtolower($education->degree), 'bachelor'))
                            Bachelor's Degree
                        @elseif(str_contains(strtolower($education->degree), 'master'))
                            Master's Degree
                        @elseif(str_contains(strtolower($education->degree), 'phd') || str_contains(strtolower($education->degree), 'doctorate'))
                            Doctorate
                        @elseif(str_contains(strtolower($education->degree), 'diploma'))
                            Diploma
                        @elseif(str_contains(strtolower($education->degree), 'certificate'))
                            Certificate
                        @else
                            Other
                        @endif
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    <span class="badge bg-{{ $education->end_date ? 'secondary' : 'success' }}">
                        {{ $education->end_date ? 'Completed' : 'In Progress' }}
                    </span>
                </div>
                
                @if($education->field_of_study)
                    <div class="mb-3">
                        <strong>Field:</strong><br>
                        <span class="badge bg-primary">{{ $education->field_of_study }}</span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
