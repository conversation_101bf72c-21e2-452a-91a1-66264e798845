@extends('admin.layout')

@section('title', 'Edit Education')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.educations.index') }}">Education</a></li>
    <li class="breadcrumb-item active">Edit Education</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Edit Education</h1>
        <p class="page-subtitle">Update your educational qualification details.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.educations.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Education
            </a>
            <a href="{{ route('admin.educations.show', $education) }}" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i>View
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    Education Details
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.educations.update', $education) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="degree" class="form-label">Degree/Qualification *</label>
                            <input type="text" class="form-control @error('degree') is-invalid @enderror" 
                                   id="degree" name="degree" value="{{ old('degree', $education->degree) }}" required
                                   placeholder="e.g., Bachelor of Science, Master's Degree, Diploma">
                            @error('degree')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="grade" class="form-label">Grade/GPA</label>
                            <input type="text" class="form-control @error('grade') is-invalid @enderror" 
                                   id="grade" name="grade" value="{{ old('grade', $education->grade) }}" 
                                   placeholder="e.g., 3.8/4.0, First Class">
                            @error('grade')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="field_of_study" class="form-label">Field of Study</label>
                        <input type="text" class="form-control @error('field_of_study') is-invalid @enderror" 
                               id="field_of_study" name="field_of_study" value="{{ old('field_of_study', $education->field_of_study) }}" 
                               placeholder="e.g., Computer Science, Information Technology, Engineering">
                        @error('field_of_study')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="institution" class="form-label">Institution *</label>
                            <input type="text" class="form-control @error('institution') is-invalid @enderror" 
                                   id="institution" name="institution" value="{{ old('institution', $education->institution) }}" required
                                   placeholder="e.g., University of Antananarivo, ESTI">
                            @error('institution')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                   id="location" name="location" value="{{ old('location', $education->location) }}" 
                                   placeholder="e.g., Antananarivo, Madagascar">
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="start_date" class="form-label">Start Date *</label>
                            <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                   id="start_date" name="start_date" value="{{ old('start_date', $education->start_date?->format('Y-m-d')) }}" required>
                            @error('start_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                   id="end_date" name="end_date" value="{{ old('end_date', $education->end_date?->format('Y-m-d')) }}">
                            <small class="text-muted">Leave empty if currently studying</small>
                            @error('end_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="order" class="form-label">Display Order</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                   id="order" name="order" value="{{ old('order', $education->order) }}" min="0">
                            <small class="text-muted">Lower numbers appear first</small>
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4"
                                  placeholder="Describe your studies, achievements, relevant coursework, projects, etc...">{{ old('description', $education->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Preview -->
                    <div class="mb-4">
                        <label class="form-label">Preview</label>
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex align-items-start">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3 flex-shrink-0">
                                    <i class="fas fa-graduation-cap text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1" id="preview-degree">{{ old('degree', $education->degree) }}</h6>
                                    <p class="text-primary mb-1" id="preview-field" style="{{ old('field_of_study', $education->field_of_study) ? '' : 'display: none;' }}">{{ old('field_of_study', $education->field_of_study) }}</p>
                                    <p class="mb-1"><strong id="preview-institution">{{ old('institution', $education->institution) }}</strong></p>
                                    <small class="text-muted" id="preview-location" style="{{ old('location', $education->location) ? '' : 'display: none;' }}">
                                        @if(old('location', $education->location))
                                            <i class="fas fa-map-marker-alt me-1"></i>{{ old('location', $education->location) }}
                                        @endif
                                    </small>
                                    <div class="mt-2">
                                        <small class="text-muted" id="preview-dates">{{ $education->date_range }}</small>
                                        <span class="badge bg-success ms-2" id="preview-grade" style="{{ old('grade', $education->grade) ? '' : 'display: none;' }}">{{ old('grade', $education->grade) }}</span>
                                    </div>
                                    <p class="mt-2 mb-0" id="preview-description" style="{{ old('description', $education->description) ? '' : 'display: none;' }}">{{ old('description', $education->description) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Education
                        </button>
                        <a href="{{ route('admin.educations.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>

                <!-- Separate Delete Form -->
                <form action="{{ route('admin.educations.destroy', $education) }}" method="POST" class="mt-3">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger"
                            onclick="return confirm('Are you sure you want to delete this education record?')">
                        <i class="fas fa-trash me-2"></i>Delete Education
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Education Info
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $education->created_at->format('M d, Y H:i') }}</small>
                </div>
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $education->updated_at->format('M d, Y H:i') }}</small>
                </div>
                <div class="mb-3">
                    <strong>Duration:</strong><br>
                    <small class="text-muted">{{ $education->date_range }}</small>
                </div>
                @if($education->grade)
                    <div class="mb-3">
                        <strong>Grade:</strong><br>
                        <span class="badge bg-success">{{ $education->grade }}</span>
                    </div>
                @endif
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Education Guidelines
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Degree Examples:</strong>
                    <ul class="small mt-2">
                        <li>Bachelor of Science (B.Sc.)</li>
                        <li>Master of Computer Science (M.C.S.)</li>
                        <li>Diploma in Information Technology</li>
                        <li>Certificate in Network Administration</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <strong>Field of Study Examples:</strong>
                    <ul class="small mt-2">
                        <li>Computer Science</li>
                        <li>Information Technology</li>
                        <li>Software Engineering</li>
                        <li>Network Administration</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Real-time preview updates
document.getElementById('degree').addEventListener('input', function() {
    document.getElementById('preview-degree').textContent = this.value || 'Degree Name';
});

document.getElementById('field_of_study').addEventListener('input', function() {
    const element = document.getElementById('preview-field');
    if (this.value) {
        element.textContent = this.value;
        element.style.display = 'block';
    } else {
        element.style.display = 'none';
    }
});

document.getElementById('institution').addEventListener('input', function() {
    document.getElementById('preview-institution').textContent = this.value || 'Institution Name';
});

document.getElementById('location').addEventListener('input', function() {
    const element = document.getElementById('preview-location');
    if (this.value) {
        element.innerHTML = '<i class="fas fa-map-marker-alt me-1"></i>' + this.value;
        element.style.display = 'block';
    } else {
        element.style.display = 'none';
    }
});

document.getElementById('grade').addEventListener('input', function() {
    const element = document.getElementById('preview-grade');
    if (this.value) {
        element.textContent = this.value;
        element.style.display = 'inline';
    } else {
        element.style.display = 'none';
    }
});

document.getElementById('description').addEventListener('input', function() {
    const element = document.getElementById('preview-description');
    if (this.value) {
        element.textContent = this.value;
        element.style.display = 'block';
    } else {
        element.style.display = 'none';
    }
});

function updateDatePreview() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const element = document.getElementById('preview-dates');
    
    if (startDate) {
        const start = new Date(startDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
        const end = endDate ? new Date(endDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short' }) : 'Present';
        element.textContent = start + ' - ' + end;
    } else {
        element.textContent = 'Start Date - End Date';
    }
}

document.getElementById('start_date').addEventListener('change', updateDatePreview);
document.getElementById('end_date').addEventListener('change', updateDatePreview);
</script>
@endpush
