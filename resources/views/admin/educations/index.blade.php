@extends('admin.layout')

@section('title', 'Education Management')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Education</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Education & Academic Background</h1>
        <p class="page-subtitle">Manage your educational qualifications and academic achievements.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.educations.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add Education
            </a>
        </div>
    </div>
</div>

@if($educations->count() > 0)
    <div class="row">
        @foreach($educations as $education)
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-graduation-cap me-2 text-primary"></i>
                            {{ $education->degree }}
                        </h5>
                        @if($education->grade)
                            <span class="badge bg-success">{{ $education->grade }}</span>
                        @endif
                    </div>
                    <div class="card-body">
                        @if($education->field_of_study)
                            <h6 class="text-primary mb-2">{{ $education->field_of_study }}</h6>
                        @endif
                        
                        <h6 class="fw-bold mb-2">{{ $education->institution }}</h6>
                        
                        @if($education->location)
                            <p class="text-muted small mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ $education->location }}
                            </p>
                        @endif
                        
                        <p class="text-muted small mb-3">
                            <i class="fas fa-calendar me-1"></i>{{ $education->date_range }}
                        </p>
                        
                        @if($education->description)
                            <p class="mb-3">{{ Str::limit($education->description, 150) }}</p>
                        @endif
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-sort me-1"></i>Order: {{ $education->order }}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.educations.show', $education) }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.educations.edit', $education) }}" class="btn btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.educations.destroy', $education) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger" 
                                            onclick="return confirm('Are you sure you want to delete this education record?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Education Timeline -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-timeline me-2"></i>
                        Education Timeline
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        @foreach($educations->sortByDesc('start_date') as $education)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">{{ $education->degree }}</h6>
                                    @if($education->field_of_study)
                                        <p class="text-primary mb-1">{{ $education->field_of_study }}</p>
                                    @endif
                                    <p class="mb-1"><strong>{{ $education->institution }}</strong></p>
                                    <small class="text-muted">{{ $education->date_range }}</small>
                                    @if($education->grade)
                                        <span class="badge bg-success ms-2">{{ $education->grade }}</span>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Education Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Education Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">{{ $educations->count() }}</h4>
                            <small class="text-muted">Total Qualifications</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">{{ $educations->whereNotNull('grade')->count() }}</h4>
                            <small class="text-muted">With Grades</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">{{ $educations->where('degree', 'like', '%Bachelor%')->count() }}</h4>
                            <small class="text-muted">Bachelor Degrees</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">{{ $educations->where('degree', 'like', '%Master%')->count() }}</h4>
                            <small class="text-muted">Master Degrees</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@else
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-graduation-cap fa-4x text-muted mb-4"></i>
            <h4 class="mb-3">No Education Records Found</h4>
            <p class="text-muted mb-4">Start building your academic profile by adding your educational background.</p>
            <a href="{{ route('admin.educations.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Add First Education
            </a>
        </div>
    </div>
@endif
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #6366f1;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #6366f1;
}
</style>
@endpush
