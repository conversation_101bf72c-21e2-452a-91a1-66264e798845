@extends('admin.layout')

@section('title', 'View Profile')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.profiles.index') }}">Profiles</a></li>
    <li class="breadcrumb-item active">{{ $profile->name }}</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">{{ $profile->name }}</h1>
        <p class="page-subtitle">{{ $profile->title }}</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.profiles.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Profiles
            </a>
            <a href="{{ route('admin.profiles.edit', $profile) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>Edit
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Profile Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Personal Information</h6>
                        <div class="mb-3">
                            <strong><i class="fas fa-user me-2 text-primary"></i>Full Name:</strong>
                            <span class="ms-2">{{ $profile->name }}</span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-briefcase me-2 text-success"></i>Title:</strong>
                            <span class="ms-2">{{ $profile->title }}</span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-envelope me-2 text-info"></i>Email:</strong>
                            <span class="ms-2">{{ $profile->email }}</span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-phone me-2 text-warning"></i>Phone:</strong>
                            <span class="ms-2">{{ $profile->phone }}</span>
                        </div>
                        <div class="mb-3">
                            <strong><i class="fas fa-map-marker-alt me-2 text-danger"></i>Address:</strong>
                            <span class="ms-2">{{ $profile->address }}</span>
                        </div>
                        @if($profile->birth_date)
                            <div class="mb-3">
                                <strong><i class="fas fa-birthday-cake me-2 text-secondary"></i>Age:</strong>
                                <span class="ms-2">{{ $profile->age }} years old</span>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Professional Details</h6>
                        <div class="mb-3">
                            <strong><i class="fas fa-briefcase me-2 text-info"></i>Freelance Status:</strong>
                            <span class="ms-2">
                                <span class="badge bg-{{ $profile->freelance_available ? 'success' : 'secondary' }}">
                                    {{ $profile->freelance_available ? 'Available' : 'Not Available' }}
                                </span>
                            </span>
                        </div>
                        @if($profile->languages && count($profile->languages) > 0)
                            <div class="mb-3">
                                <strong><i class="fas fa-language me-2 text-warning"></i>Languages:</strong>
                                <div class="ms-2 mt-2">
                                    @foreach($profile->languages as $language)
                                        <span class="badge bg-primary me-1">{{ $language }}</span>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        @if($profile->website)
                            <div class="mb-3">
                                <strong><i class="fas fa-globe me-2 text-primary"></i>Website:</strong>
                                <span class="ms-2">
                                    <a href="{{ $profile->website }}" target="_blank" class="text-decoration-none">
                                        {{ $profile->website }}
                                        <i class="fas fa-external-link-alt ms-1 small"></i>
                                    </a>
                                </span>
                            </div>
                        @endif
                        @if($profile->cv_url)
                            <div class="mb-3">
                                <strong><i class="fas fa-file-pdf me-2 text-danger"></i>CV:</strong>
                                <span class="ms-2">
                                    <a href="{{ $profile->cv_url }}" target="_blank" class="text-decoration-none">
                                        Download CV
                                        <i class="fas fa-download ms-1 small"></i>
                                    </a>
                                </span>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary mb-3">Professional Description</h6>
                    <div class="bg-light p-3 rounded">
                        <p class="mb-0">{{ $profile->description }}</p>
                    </div>
                </div>

                @if($profile->linkedin || $profile->github || $profile->twitter)
                    <div class="mb-4">
                        <h6 class="text-primary mb-3">Social Media</h6>
                        <div class="d-flex gap-3">
                            @if($profile->linkedin)
                                <a href="{{ $profile->linkedin }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="fab fa-linkedin me-2"></i>LinkedIn
                                </a>
                            @endif
                            @if($profile->github)
                                <a href="{{ $profile->github }}" target="_blank" class="btn btn-outline-dark">
                                    <i class="fab fa-github me-2"></i>GitHub
                                </a>
                            @endif
                            @if($profile->twitter)
                                <a href="{{ $profile->twitter }}" target="_blank" class="btn btn-outline-info">
                                    <i class="fab fa-twitter me-2"></i>Twitter
                                </a>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
            <div class="card-footer bg-transparent">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-calendar-plus me-1"></i>
                            Created: {{ $profile->created_at->format('M d, Y H:i') }}
                        </small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">
                            <i class="fas fa-calendar-edit me-1"></i>
                            Updated: {{ $profile->updated_at->format('M d, Y H:i') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user-circle me-2"></i>
                    Profile Preview
                </h6>
            </div>
            <div class="card-body text-center">
                @if($profile->avatar)
                    <img src="{{ $profile->avatar_url }}" alt="{{ $profile->name }}"
                         class="img-fluid rounded-circle mb-3" style="max-width: 150px; height: 150px; object-fit: cover;">
                @else
                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 150px; height: 150px;">
                        <i class="fas fa-user fa-4x text-muted"></i>
                    </div>
                @endif
                
                <h5>{{ $profile->name }}</h5>
                <p class="text-primary">{{ $profile->title }}</p>
                <p class="text-muted small">{{ $profile->address }}</p>
                
                <div class="d-flex justify-content-center gap-2 mb-3">
                    @if($profile->website)
                        <a href="{{ $profile->website }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-globe"></i>
                        </a>
                    @endif
                    @if($profile->linkedin)
                        <a href="{{ $profile->linkedin }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    @endif
                    @if($profile->github)
                        <a href="{{ $profile->github }}" target="_blank" class="btn btn-sm btn-outline-dark">
                            <i class="fab fa-github"></i>
                        </a>
                    @endif
                    @if($profile->cv_url)
                        <a href="{{ $profile->cv_url }}" target="_blank" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-file-pdf"></i>
                        </a>
                    @endif
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-primary">{{ $profile->freelance_available ? 'Yes' : 'No' }}</h6>
                        <small class="text-muted">Freelance</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success">{{ $profile->languages ? count($profile->languages) : 0 }}</h6>
                        <small class="text-muted">Languages</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.profiles.edit', $profile) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </a>
                    <a href="{{ url('/') }}" target="_blank" class="btn btn-success">
                        <i class="fas fa-eye me-2"></i>View Live Portfolio
                    </a>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Profile Stats
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-primary">{{ $profile->birth_date ? $profile->age : 'N/A' }}</h5>
                        <small class="text-muted">Age</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success">{{ $profile->created_at->diffForHumans() }}</h5>
                        <small class="text-muted">Created</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
