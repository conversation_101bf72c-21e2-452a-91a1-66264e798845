@extends('admin.layout')

@section('title', 'Create Profile')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.profiles.index') }}">Profiles</a></li>
    <li class="breadcrumb-item active">Create Profile</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Create New Profile</h1>
        <p class="page-subtitle">Set up your personal information and portfolio details.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.profiles.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Profiles
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Profile Information
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.profiles.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">Professional Title *</label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required
                                   placeholder="e.g., DevOps Engineer, Full Stack Developer">
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Professional Description *</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" required
                                  placeholder="Brief description of your professional background and expertise...">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                   id="email" name="email" value="{{ old('email') }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number *</label>
                            <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                   id="phone" name="phone" value="{{ old('phone') }}" required>
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="address" class="form-label">Address *</label>
                            <input type="text" class="form-control @error('address') is-invalid @enderror" 
                                   id="address" name="address" value="{{ old('address') }}" required
                                   placeholder="City, Country">
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="birth_date" class="form-label">Birth Date</label>
                            <input type="date" class="form-control @error('birth_date') is-invalid @enderror" 
                                   id="birth_date" name="birth_date" value="{{ old('birth_date') }}">
                            @error('birth_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="avatar" class="form-label">Profile Photo</label>
                            <input type="file" class="form-control @error('avatar') is-invalid @enderror" 
                                   id="avatar" name="avatar" accept="image/*">
                            <small class="text-muted">Accepted formats: JPG, PNG, GIF (max 2MB)</small>
                            @error('avatar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="website" class="form-label">Website URL</label>
                            <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                   id="website" name="website" value="{{ old('website') }}"
                                   placeholder="https://your-website.com">
                            @error('website')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cv_url" class="form-label">CV Download URL</label>
                            <input type="url" class="form-control @error('cv_url') is-invalid @enderror" 
                                   id="cv_url" name="cv_url" value="{{ old('cv_url') }}"
                                   placeholder="https://link-to-your-cv.pdf">
                            @error('cv_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="linkedin" class="form-label">LinkedIn Profile</label>
                            <input type="url" class="form-control @error('linkedin') is-invalid @enderror" 
                                   id="linkedin" name="linkedin" value="{{ old('linkedin') }}"
                                   placeholder="https://linkedin.com/in/yourprofile">
                            @error('linkedin')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="github" class="form-label">GitHub Profile</label>
                            <input type="url" class="form-control @error('github') is-invalid @enderror" 
                                   id="github" name="github" value="{{ old('github') }}"
                                   placeholder="https://github.com/yourusername">
                            @error('github')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="twitter" class="form-label">Twitter Profile</label>
                            <input type="url" class="form-control @error('twitter') is-invalid @enderror" 
                                   id="twitter" name="twitter" value="{{ old('twitter') }}"
                                   placeholder="https://twitter.com/yourusername">
                            @error('twitter')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Languages</label>
                        <div id="languages-container">
                            @if(old('languages'))
                                @foreach(old('languages') as $index => $language)
                                    <div class="input-group mb-2 language-item">
                                        <input type="text" class="form-control" name="languages[]" 
                                               value="{{ $language }}" placeholder="e.g., English, French, Malagasy">
                                        <button class="btn btn-outline-danger" type="button" onclick="removeLanguage(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="input-group mb-2 language-item">
                                    <input type="text" class="form-control" name="languages[]" 
                                           placeholder="e.g., English, French, Malagasy">
                                    <button class="btn btn-outline-danger" type="button" onclick="removeLanguage(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addLanguage()">
                            <i class="fas fa-plus me-1"></i>Add Language
                        </button>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="freelance_available" name="freelance_available" 
                                   value="1" {{ old('freelance_available') ? 'checked' : '' }}>
                            <label class="form-check-label" for="freelance_available">
                                <strong>Available for freelance work</strong>
                            </label>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Profile
                        </button>
                        <a href="{{ route('admin.profiles.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Profile Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use a professional photo for your avatar</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Write a compelling professional description</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Include all relevant contact information</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Add your social media profiles</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Upload your latest CV</li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-camera me-2"></i>
                    Photo Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use a high-quality, professional photo</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Square format works best (1:1 ratio)</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Maximum file size: 2MB</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Supported formats: JPG, PNG, GIF</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Good lighting and clear background</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function addLanguage() {
    const container = document.getElementById('languages-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2 language-item';
    div.innerHTML = `
        <input type="text" class="form-control" name="languages[]" placeholder="e.g., English, French, Malagasy">
        <button class="btn btn-outline-danger" type="button" onclick="removeLanguage(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeLanguage(button) {
    const container = document.getElementById('languages-container');
    if (container.children.length > 1) {
        button.closest('.language-item').remove();
    }
}
</script>
@endpush
