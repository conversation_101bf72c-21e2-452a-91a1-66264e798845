@extends('admin.layout')

@section('title', 'Edit Profile')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.profiles.index') }}">Profiles</a></li>
    <li class="breadcrumb-item active">Edit Profile</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Edit Profile</h1>
        <p class="page-subtitle">Update your personal information and portfolio details.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.profiles.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Profiles
            </a>
            <a href="{{ route('admin.profiles.show', $profile) }}" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i>View
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Profile Information
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.profiles.update', $profile) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $profile->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">Professional Title *</label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $profile->title) }}" required
                                   placeholder="e.g., DevOps Engineer, Full Stack Developer">
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Professional Description *</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" required
                                  placeholder="Brief description of your professional background and expertise...">{{ old('description', $profile->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                   id="email" name="email" value="{{ old('email', $profile->email) }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number *</label>
                            <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                   id="phone" name="phone" value="{{ old('phone', $profile->phone) }}" required>
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="address" class="form-label">Address *</label>
                            <input type="text" class="form-control @error('address') is-invalid @enderror" 
                                   id="address" name="address" value="{{ old('address', $profile->address) }}" required
                                   placeholder="City, Country">
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="birth_date" class="form-label">Birth Date</label>
                            <input type="date" class="form-control @error('birth_date') is-invalid @enderror" 
                                   id="birth_date" name="birth_date" value="{{ old('birth_date', $profile->birth_date?->format('Y-m-d')) }}">
                            @error('birth_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="website" class="form-label">Website URL</label>
                            <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                   id="website" name="website" value="{{ old('website', $profile->website) }}"
                                   placeholder="https://your-website.com">
                            @error('website')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="cv_url" class="form-label">CV Download URL</label>
                            <input type="url" class="form-control @error('cv_url') is-invalid @enderror" 
                                   id="cv_url" name="cv_url" value="{{ old('cv_url', $profile->cv_url) }}"
                                   placeholder="https://link-to-your-cv.pdf">
                            @error('cv_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="linkedin" class="form-label">LinkedIn Profile</label>
                            <input type="url" class="form-control @error('linkedin') is-invalid @enderror" 
                                   id="linkedin" name="linkedin" value="{{ old('linkedin', $profile->linkedin) }}"
                                   placeholder="https://linkedin.com/in/yourprofile">
                            @error('linkedin')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="github" class="form-label">GitHub Profile</label>
                            <input type="url" class="form-control @error('github') is-invalid @enderror" 
                                   id="github" name="github" value="{{ old('github', $profile->github) }}"
                                   placeholder="https://github.com/yourusername">
                            @error('github')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="twitter" class="form-label">Twitter Profile</label>
                            <input type="url" class="form-control @error('twitter') is-invalid @enderror" 
                                   id="twitter" name="twitter" value="{{ old('twitter', $profile->twitter) }}"
                                   placeholder="https://twitter.com/yourusername">
                            @error('twitter')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="avatar" class="form-label">Profile Photo</label>
                            <input type="file" class="form-control @error('avatar') is-invalid @enderror"
                                   id="avatar" name="avatar" accept="image/*">
                            <small class="text-muted">Accepted formats: JPG, PNG, GIF (max 2MB)</small>
                            @error('avatar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            @if($profile->avatar)
                                <div class="mt-2">
                                    <small class="text-success">
                                        <i class="fas fa-check me-1"></i>Current photo uploaded
                                    </small>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Languages</label>
                        <div id="languages-container">
                            @php
                                $languages = old('languages', $profile->languages ?? []);
                            @endphp
                            @if($languages && count($languages) > 0)
                                @foreach($languages as $index => $language)
                                    <div class="input-group mb-2 language-item">
                                        <input type="text" class="form-control" name="languages[]" 
                                               value="{{ $language }}" placeholder="e.g., English, French, Malagasy">
                                        <button class="btn btn-outline-danger" type="button" onclick="removeLanguage(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="input-group mb-2 language-item">
                                    <input type="text" class="form-control" name="languages[]" 
                                           placeholder="e.g., English, French, Malagasy">
                                    <button class="btn btn-outline-danger" type="button" onclick="removeLanguage(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addLanguage()">
                            <i class="fas fa-plus me-1"></i>Add Language
                        </button>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="freelance_available" name="freelance_available" 
                                   value="1" {{ old('freelance_available', $profile->freelance_available) ? 'checked' : '' }}>
                            <label class="form-check-label" for="freelance_available">
                                <strong>Available for freelance work</strong>
                            </label>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                        <a href="{{ route('admin.profiles.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Profile Info
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $profile->created_at->format('M d, Y H:i') }}</small>
                </div>
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $profile->updated_at->format('M d, Y H:i') }}</small>
                </div>
                <div class="mb-3">
                    <strong>Age:</strong><br>
                    <small class="text-muted">{{ $profile->age ?? 'Not specified' }} years old</small>
                </div>
                <div class="mb-3">
                    <strong>Freelance Status:</strong><br>
                    <span class="badge bg-{{ $profile->freelance_available ? 'success' : 'secondary' }}">
                        {{ $profile->freelance_available ? 'Available' : 'Not Available' }}
                    </span>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Preview
                </h6>
            </div>
            <div class="card-body text-center">
                @if($profile->avatar)
                    <img src="{{ $profile->avatar_url }}" alt="{{ $profile->name }}"
                         class="img-fluid rounded-circle mb-3" style="max-width: 100px; height: 100px; object-fit: cover;">
                @else
                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 100px; height: 100px;">
                        <i class="fas fa-user fa-2x text-muted"></i>
                    </div>
                @endif
                
                <h6>{{ $profile->name }}</h6>
                <p class="text-primary small">{{ $profile->title }}</p>
                
                <div class="d-flex justify-content-center gap-2">
                    @if($profile->website)
                        <a href="{{ $profile->website }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-globe"></i>
                        </a>
                    @endif
                    @if($profile->linkedin)
                        <a href="{{ $profile->linkedin }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    @endif
                    @if($profile->github)
                        <a href="{{ $profile->github }}" target="_blank" class="btn btn-sm btn-outline-dark">
                            <i class="fab fa-github"></i>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function addLanguage() {
    const container = document.getElementById('languages-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2 language-item';
    div.innerHTML = `
        <input type="text" class="form-control" name="languages[]" placeholder="e.g., English, French, Malagasy">
        <button class="btn btn-outline-danger" type="button" onclick="removeLanguage(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeLanguage(button) {
    const container = document.getElementById('languages-container');
    if (container.children.length > 1) {
        button.closest('.language-item').remove();
    }
}
</script>
@endpush
