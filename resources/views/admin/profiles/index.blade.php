@extends('admin.layout')

@section('title', 'Profile Management')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Profiles</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Profile Management</h1>
        <p class="page-subtitle">Manage your personal information and portfolio details.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            @if($profiles->count() == 0)
                <a href="{{ route('admin.profiles.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create Profile
                </a>
            @endif
        </div>
    </div>
</div>

@if($profiles->count() > 0)
    @foreach($profiles as $profile)
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    {{ $profile->name }}
                </h5>
                <div>
                    <a href="{{ route('admin.profiles.edit', $profile) }}" class="btn btn-sm btn-warning">
                        <i class="fas fa-edit me-1"></i>Edit
                    </a>
                    <a href="{{ route('admin.profiles.show', $profile) }}" class="btn btn-sm btn-info">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="text-primary mb-3">{{ $profile->title }}</h6>
                        <p class="mb-3">{{ Str::limit($profile->description, 200) }}</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong><i class="fas fa-envelope me-2 text-primary"></i>Email:</strong>
                                    <span class="ms-2">{{ $profile->email }}</span>
                                </div>
                                <div class="mb-3">
                                    <strong><i class="fas fa-phone me-2 text-success"></i>Phone:</strong>
                                    <span class="ms-2">{{ $profile->phone }}</span>
                                </div>
                                <div class="mb-3">
                                    <strong><i class="fas fa-map-marker-alt me-2 text-danger"></i>Location:</strong>
                                    <span class="ms-2">{{ $profile->address }}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong><i class="fas fa-birthday-cake me-2 text-warning"></i>Age:</strong>
                                    <span class="ms-2">{{ $profile->age ?? 'N/A' }} years</span>
                                </div>
                                <div class="mb-3">
                                    <strong><i class="fas fa-briefcase me-2 text-info"></i>Freelance:</strong>
                                    <span class="ms-2">
                                        <span class="badge bg-{{ $profile->freelance_available ? 'success' : 'secondary' }}">
                                            {{ $profile->freelance_available ? 'Available' : 'Not Available' }}
                                        </span>
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <strong><i class="fas fa-language me-2 text-purple"></i>Languages:</strong>
                                    <span class="ms-2">
                                        {{ is_array($profile->languages) ? implode(', ', $profile->languages) : $profile->languages }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            @if($profile->avatar)
                                <img src="{{ $profile->avatar_url }}" alt="{{ $profile->name }}"
                                     class="img-fluid rounded-circle mb-3" style="max-width: 150px; height: 150px; object-fit: cover;">
                            @else
                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                                     style="width: 150px; height: 150px;">
                                    <i class="fas fa-user fa-4x text-muted"></i>
                                </div>
                            @endif
                            
                            <div class="d-flex flex-wrap justify-content-center gap-2">
                                @if($profile->website)
                                    <a href="{{ $profile->website }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-globe"></i>
                                    </a>
                                @endif
                                @if($profile->linkedin)
                                    <a href="{{ $profile->linkedin }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fab fa-linkedin"></i>
                                    </a>
                                @endif
                                @if($profile->github)
                                    <a href="{{ $profile->github }}" target="_blank" class="btn btn-sm btn-outline-dark">
                                        <i class="fab fa-github"></i>
                                    </a>
                                @endif
                                @if($profile->cv_url)
                                    <a href="{{ $profile->cv_url }}" target="_blank" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-muted">
                <div class="row">
                    <div class="col-md-6">
                        <small><i class="fas fa-calendar-plus me-1"></i>Created: {{ $profile->created_at->format('M d, Y H:i') }}</small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small><i class="fas fa-calendar-edit me-1"></i>Updated: {{ $profile->updated_at->format('M d, Y H:i') }}</small>
                    </div>
                </div>
            </div>
        </div>
    @endforeach
@else
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-user fa-4x text-muted mb-4"></i>
            <h4 class="mb-3">No Profile Found</h4>
            <p class="text-muted mb-4">Create your first profile to get started with your portfolio.</p>
            <a href="{{ route('admin.profiles.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Create Profile
            </a>
        </div>
    </div>
@endif
@endsection
