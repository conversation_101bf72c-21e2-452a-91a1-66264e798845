@extends('admin.layout')

@section('title', 'Edit Skill')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.skills.index') }}">Skills</a></li>
    <li class="breadcrumb-item active">Edit Skill</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Edit Skill</h1>
        <p class="page-subtitle">Update your technical skill details and proficiency level.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.skills.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Skills
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Skill Details
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.skills.update', $skill) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="name" class="form-label">Skill Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $skill->name) }}" required
                                   placeholder="e.g., Laravel, Docker, Linux Administration">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="level" class="form-label">Proficiency Level * (%)</label>
                            <input type="range" class="form-range" id="level" name="level" 
                                   min="1" max="100" value="{{ old('level', $skill->level) }}" 
                                   oninput="updateLevelDisplay(this.value)">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Beginner</small>
                                <span id="level-display" class="badge bg-primary">{{ old('level', $skill->level) }}%</span>
                                <small class="text-muted">Expert</small>
                            </div>
                            @error('level')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">Category *</label>
                            <select class="form-select @error('category') is-invalid @enderror" 
                                    id="category" name="category" required onchange="updateCategoryIcon()">
                                <option value="">Select Category</option>
                                <option value="technical" {{ old('category', $skill->category) == 'technical' ? 'selected' : '' }}>Technical</option>
                                <option value="soft" {{ old('category', $skill->category) == 'soft' ? 'selected' : '' }}>Soft Skills</option>
                                <option value="language" {{ old('category', $skill->category) == 'language' ? 'selected' : '' }}>Programming Language</option>
                                <option value="tool" {{ old('category', $skill->category) == 'tool' ? 'selected' : '' }}>Tool</option>
                                <option value="framework" {{ old('category', $skill->category) == 'framework' ? 'selected' : '' }}>Framework</option>
                                <option value="other" {{ old('category', $skill->category) == 'other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('category')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="icon" class="form-label">Icon (Font Awesome class)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i id="icon-preview" class="{{ old('icon', $skill->icon ?: 'fas fa-cog') }}"></i>
                                </span>
                                <input type="text" class="form-control @error('icon') is-invalid @enderror" 
                                       id="icon" name="icon" value="{{ old('icon', $skill->icon) }}" 
                                       placeholder="e.g., fab fa-laravel, fas fa-server"
                                       oninput="updateIconPreview(this.value)">
                            </div>
                            <small class="text-muted">Visit <a href="https://fontawesome.com/icons" target="_blank">FontAwesome</a> for icon classes</small>
                            @error('icon')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3"
                                      placeholder="Brief description of your experience with this skill...">{{ old('description', $skill->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="order" class="form-label">Display Order</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                   id="order" name="order" value="{{ old('order', $skill->order) }}" min="0">
                            <small class="text-muted">Lower numbers appear first</small>
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Preview -->
                    <div class="mb-4">
                        <label class="form-label">Preview</label>
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex align-items-center mb-2">
                                <i id="preview-icon" class="{{ old('icon', $skill->icon ?: 'fas fa-cog') }} me-2 text-primary"></i>
                                <h6 class="mb-0" id="preview-name">{{ old('name', $skill->name) }}</h6>
                                <span class="badge bg-primary ms-2" id="preview-level">{{ old('level', $skill->level) }}%</span>
                            </div>
                            <div class="progress mb-2" style="height: 6px;">
                                <div class="progress-bar bg-primary" id="preview-progress" 
                                     style="width: {{ old('level', $skill->level) }}%"></div>
                            </div>
                            <small class="text-muted" id="preview-description">{{ old('description', $skill->description ?: 'Skill description will appear here...') }}</small>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Skill
                        </button>
                        <a href="{{ route('admin.skills.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>

                <!-- Separate Delete Form -->
                <form action="{{ route('admin.skills.destroy', $skill) }}" method="POST" class="mt-3">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger"
                            onclick="return confirm('Are you sure you want to delete this skill?')">
                        <i class="fas fa-trash me-2"></i>Delete Skill
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Skill Info
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Category:</strong><br>
                    <span class="badge bg-primary">{{ ucfirst($skill->category) }}</span>
                </div>
                <div class="mb-3">
                    <strong>Current Level:</strong><br>
                    <div class="progress mb-1" style="height: 8px;">
                        <div class="progress-bar bg-primary" style="width: {{ $skill->level }}%"></div>
                    </div>
                    <small class="text-muted">{{ $skill->level }}% proficiency</small>
                </div>
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ $skill->created_at->format('M d, Y H:i') }}</small>
                </div>
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted">{{ $skill->updated_at->format('M d, Y H:i') }}</small>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Proficiency Guidelines
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small">Beginner</span>
                        <span class="badge bg-danger">1-30%</span>
                    </div>
                    <small class="text-muted">Basic understanding, limited experience</small>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small">Intermediate</span>
                        <span class="badge bg-warning">31-60%</span>
                    </div>
                    <small class="text-muted">Good working knowledge, some projects</small>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small">Advanced</span>
                        <span class="badge bg-info">61-80%</span>
                    </div>
                    <small class="text-muted">Strong expertise, multiple projects</small>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small">Expert</span>
                        <span class="badge bg-success">81-100%</span>
                    </div>
                    <small class="text-muted">Deep expertise, can teach others</small>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    Popular Icons
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fab fa-laravel')">
                            <i class="fab fa-laravel"></i><br><small>Laravel</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fab fa-php')">
                            <i class="fab fa-php"></i><br><small>PHP</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fab fa-js')">
                            <i class="fab fa-js"></i><br><small>JavaScript</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fab fa-docker')">
                            <i class="fab fa-docker"></i><br><small>Docker</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fab fa-linux')">
                            <i class="fab fa-linux"></i><br><small>Linux</small>
                        </button>
                    </div>
                    <div class="col-4 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setIcon('fas fa-database')">
                            <i class="fas fa-database"></i><br><small>Database</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateLevelDisplay(value) {
    document.getElementById('level-display').textContent = value + '%';
    document.getElementById('preview-level').textContent = value + '%';
    document.getElementById('preview-progress').style.width = value + '%';
}

function updateIconPreview(iconClass) {
    if (iconClass) {
        document.getElementById('icon-preview').className = iconClass;
        document.getElementById('preview-icon').className = iconClass + ' me-2 text-primary';
    }
}

function updateCategoryIcon() {
    const category = document.getElementById('category').value;
    const iconMap = {
        'technical': 'fas fa-code',
        'soft': 'fas fa-users',
        'language': 'fas fa-language',
        'tool': 'fas fa-tools',
        'framework': 'fas fa-layer-group',
        'other': 'fas fa-cog'
    };
    
    if (iconMap[category] && !document.getElementById('icon').value) {
        setIcon(iconMap[category]);
    }
}

function setIcon(iconClass) {
    document.getElementById('icon').value = iconClass;
    updateIconPreview(iconClass);
}

// Real-time preview updates
document.getElementById('name').addEventListener('input', function() {
    document.getElementById('preview-name').textContent = this.value || 'Skill Name';
});

document.getElementById('description').addEventListener('input', function() {
    document.getElementById('preview-description').textContent = this.value || 'Skill description will appear here...';
});
</script>
@endpush
