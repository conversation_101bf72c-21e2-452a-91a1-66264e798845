@extends('admin.layout')

@section('title', 'Skills Management')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Skills</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Technical Skills</h1>
        <p class="page-subtitle">Manage your technical expertise and proficiency levels.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.skills.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add Skill
            </a>
        </div>
    </div>
</div>

@if($skills->count() > 0)
    <!-- Skills by Category -->
    @php
        $skillsByCategory = $skills->groupBy('category');
        $categoryColors = [
            'technical' => 'primary',
            'soft' => 'success',
            'language' => 'info',
            'tool' => 'warning',
            'framework' => 'danger',
            'other' => 'secondary'
        ];
        $categoryIcons = [
            'technical' => 'fas fa-code',
            'soft' => 'fas fa-users',
            'language' => 'fas fa-language',
            'tool' => 'fas fa-tools',
            'framework' => 'fas fa-layer-group',
            'other' => 'fas fa-cog'
        ];
    @endphp

    @foreach($skillsByCategory as $category => $categorySkills)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="{{ $categoryIcons[$category] ?? 'fas fa-cog' }} me-2 text-{{ $categoryColors[$category] ?? 'secondary' }}"></i>
                    {{ ucfirst($category) }} Skills
                    <span class="badge bg-{{ $categoryColors[$category] ?? 'secondary' }} ms-2">{{ $categorySkills->count() }}</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($categorySkills as $skill)
                        <div class="col-lg-6 mb-3">
                            <div class="d-flex align-items-center justify-content-between p-3 border rounded">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        @if($skill->icon)
                                            <i class="{{ $skill->icon }} me-2 text-{{ $categoryColors[$category] ?? 'secondary' }}"></i>
                                        @endif
                                        <h6 class="mb-0">{{ $skill->name }}</h6>
                                        <span class="badge bg-{{ $categoryColors[$category] ?? 'secondary' }} ms-2">{{ $skill->level }}%</span>
                                    </div>
                                    
                                    <!-- Progress Bar -->
                                    <div class="progress mb-2" style="height: 6px;">
                                        <div class="progress-bar bg-{{ $categoryColors[$category] ?? 'secondary' }}" 
                                             style="width: {{ $skill->level }}%"></div>
                                    </div>
                                    
                                    @if($skill->description)
                                        <small class="text-muted">{{ Str::limit($skill->description, 80) }}</small>
                                    @endif
                                </div>
                                
                                <div class="ms-3">
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.skills.edit', $skill) }}" class="btn btn-outline-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.skills.destroy', $skill) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" 
                                                    onclick="return confirm('Are you sure you want to delete this skill?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endforeach

    <!-- Skills Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Skills Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <h4 class="text-primary">{{ $skills->count() }}</h4>
                            <small class="text-muted">Total Skills</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-success">{{ $skills->where('level', '>=', 80)->count() }}</h4>
                            <small class="text-muted">Expert Level</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-info">{{ $skills->where('category', 'technical')->count() }}</h4>
                            <small class="text-muted">Technical</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-warning">{{ $skills->where('category', 'tool')->count() }}</h4>
                            <small class="text-muted">Tools</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-danger">{{ $skills->where('category', 'framework')->count() }}</h4>
                            <small class="text-muted">Frameworks</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-secondary">{{ number_format($skills->avg('level'), 1) }}%</h4>
                            <small class="text-muted">Avg. Level</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@else
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-cogs fa-4x text-muted mb-4"></i>
            <h4 class="mb-3">No Skills Found</h4>
            <p class="text-muted mb-4">Start showcasing your expertise by adding your first technical skill.</p>
            <a href="{{ route('admin.skills.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Add First Skill
            </a>
        </div>
    </div>
@endif
@endsection
