@extends('admin.layout')

@section('title', 'Dashboard')

@section('breadcrumb')
    <li class="breadcrumb-item active">Dashboard</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Dashboard</h1>
        <p class="page-subtitle">Welcome back! Here's what's happening with your portfolio.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url('/') }}" class="btn btn-outline-primary" target="_blank">
                <i class="fas fa-eye me-2"></i>View Portfolio
            </a>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Profile</div>
                        <div class="stats-number">{{ $stats['profiles'] }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Experiences</div>
                        <div class="stats-number">{{ $stats['experiences'] }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-briefcase fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Skills</div>
                        <div class="stats-number">{{ $stats['skills'] }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cogs fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Education</div>
                        <div class="stats-number">{{ $stats['educations'] }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-graduation-cap fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.profiles.index') }}" class="btn btn-primary w-100">
                            <i class="fas fa-user me-2"></i>
                            Manage Profile
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.experiences.create') }}" class="btn btn-success w-100">
                            <i class="fas fa-plus me-2"></i>
                            Add Experience
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.skills.create') }}" class="btn btn-warning w-100">
                            <i class="fas fa-plus me-2"></i>
                            Add Skill
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('admin.certifications.create') }}" class="btn btn-info w-100">
                            <i class="fas fa-plus me-2"></i>
                            Add Certification
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Content -->
<div class="row">
    <!-- Recent Experiences -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    Recent Experiences
                </h5>
                <a href="{{ route('admin.experiences.index') }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                @if($recentExperiences->count() > 0)
                    @foreach($recentExperiences as $experience)
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                <i class="fas fa-briefcase text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $experience->position }}</h6>
                                <small class="text-muted">{{ $experience->company }}</small>
                            </div>
                            <div>
                                <span class="badge bg-primary">{{ $experience->current ? 'Current' : 'Past' }}</span>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No experiences yet</p>
                        <a href="{{ route('admin.experiences.create') }}" class="btn btn-primary">
                            Add First Experience
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Recent Skills -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Recent Skills
                </h5>
                <a href="{{ route('admin.skills.index') }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                @if($recentSkills->count() > 0)
                    @foreach($recentSkills as $skill)
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3">
                                    <i class="fas fa-cog text-warning"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ $skill->name }}</h6>
                                    <small class="text-muted">{{ ucfirst($skill->category) }}</small>
                                </div>
                            </div>
                            <div>
                                <span class="badge bg-warning">{{ $skill->level }}%</span>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No skills yet</p>
                        <a href="{{ route('admin.skills.create') }}" class="btn btn-warning">
                            Add First Skill
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- System Info -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Portfolio System:</strong> Dynamic Laravel-based</p>
                        <p><strong>Database:</strong> MySQL ({{ array_sum($stats) }} total records)</p>
                        <p><strong>Authentication:</strong> Laravel Auth</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Last Updated:</strong> {{ now()->format('Y-m-d H:i:s') }}</p>
                        <p><strong>Status:</strong> <span class="badge bg-success">Active</span></p>
                        <p><strong>Version:</strong> 2.0 (Admin Panel)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
