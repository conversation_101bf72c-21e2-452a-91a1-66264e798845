@extends('admin.layout')

@section('title', 'Détails du Projet')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.projects.index') }}">Projects & Labs</a></li>
    <li class="breadcrumb-item active">{{ $project->title }}</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">{{ $project->title }}</h1>
        <p class="page-subtitle">Détails complets du projet</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.projects.edit', $project) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Modifier
            </a>
            <a href="{{ route('admin.projects.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour à la liste
            </a>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="fas fa-trash me-2"></i>Supprimer
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations générales
                    </h5>
                    <div>
                        <span class="badge bg-{{ $project->type_badge['class'] }} me-2">
                            <i class="fas fa-{{ $project->type === 'project' ? 'folder' : ($project->type === 'lab' ? 'flask' : 'desktop') }} me-1"></i>
                            {{ $project->type_badge['text'] }}
                        </span>
                        <span class="badge bg-{{ $project->status_badge['class'] }}">
                            <i class="fas fa-{{ $project->status === 'completed' ? 'check' : ($project->status === 'in_progress' ? 'clock' : 'calendar') }} me-1"></i>
                            {{ $project->status_badge['text'] }}
                        </span>
                        @if($project->is_featured)
                            <span class="badge bg-warning text-dark ms-2">
                                <i class="fas fa-star me-1"></i>Mis en avant
                            </span>
                        @endif
                        @if(!$project->is_active)
                            <span class="badge bg-secondary ms-2">
                                <i class="fas fa-eye-slash me-1"></i>Inactif
                            </span>
                        @endif
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if($project->image)
                    <div class="mb-4 text-center">
                        <img src="{{ $project->image_url }}" alt="{{ $project->title }}" 
                             class="img-fluid rounded shadow-sm" style="max-height: 300px;">
                    </div>
                @endif

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Titre</h6>
                        <p class="h5">{{ $project->title }}</p>
                    </div>
                    @if($project->category)
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Catégorie</h6>
                            <p><span class="badge bg-secondary">{{ $project->category }}</span></p>
                        </div>
                    @endif
                </div>

                <div class="mb-4">
                    <h6 class="text-muted mb-2">Description</h6>
                    <p class="lead">{{ $project->description }}</p>
                </div>

                @if($project->technologies && count($project->technologies) > 0)
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Technologies utilisées</h6>
                        <div>
                            @foreach($project->technologies as $tech)
                                <span class="badge bg-light text-dark me-2 mb-2 p-2">{{ $tech }}</span>
                            @endforeach
                        </div>
                    </div>
                @endif

                <div class="row mb-4">
                    @if($project->start_date)
                        <div class="col-md-4">
                            <h6 class="text-muted mb-2">Date de début</h6>
                            <p><i class="fas fa-calendar me-2"></i>{{ $project->start_date->format('d/m/Y') }}</p>
                        </div>
                    @endif
                    @if($project->end_date)
                        <div class="col-md-4">
                            <h6 class="text-muted mb-2">Date de fin</h6>
                            <p><i class="fas fa-calendar me-2"></i>{{ $project->end_date->format('d/m/Y') }}</p>
                        </div>
                    @endif
                    @if($project->duration)
                        <div class="col-md-4">
                            <h6 class="text-muted mb-2">Durée</h6>
                            <p><i class="fas fa-clock me-2"></i>{{ $project->duration }}</p>
                        </div>
                    @endif
                </div>

                @if($project->project_url || $project->github_url || $project->demo_url)
                    <div class="mb-4">
                        <h6 class="text-muted mb-3">Liens</h6>
                        <div class="d-flex flex-wrap gap-2">
                            @if($project->project_url)
                                <a href="{{ $project->project_url }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-2"></i>Voir le projet
                                </a>
                            @endif
                            @if($project->github_url)
                                <a href="{{ $project->github_url }}" target="_blank" class="btn btn-outline-dark">
                                    <i class="fab fa-github me-2"></i>GitHub
                                </a>
                            @endif
                            @if($project->demo_url)
                                <a href="{{ $project->demo_url }}" target="_blank" class="btn btn-outline-success">
                                    <i class="fas fa-play me-2"></i>Démonstration
                                </a>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Statistiques
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border rounded p-3 mb-3">
                            <div class="h4 mb-1 text-primary">{{ $project->order }}</div>
                            <small class="text-muted">Ordre</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3 mb-3">
                            <div class="h4 mb-1 text-info">{{ count($project->technologies ?? []) }}</div>
                            <small class="text-muted">Technologies</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Historique
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Projet créé</h6>
                            <p class="timeline-text text-muted">{{ $project->created_at->format('d/m/Y à H:i') }}</p>
                        </div>
                    </div>
                    @if($project->updated_at != $project->created_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Dernière modification</h6>
                                <p class="timeline-text text-muted">{{ $project->updated_at->format('d/m/Y à H:i') }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>Actions rapides
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.projects.edit', $project) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Modifier le projet
                    </a>
                    @if($project->is_active)
                        <form action="{{ route('admin.projects.update', $project) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_active" value="0">
                            <button type="submit" class="btn btn-warning w-100">
                                <i class="fas fa-eye-slash me-2"></i>Désactiver
                            </button>
                        </form>
                    @else
                        <form action="{{ route('admin.projects.update', $project) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_active" value="1">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-eye me-2"></i>Activer
                            </button>
                        </form>
                    @endif
                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-2"></i>Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>Confirmer la suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le projet <strong>"{{ $project->title }}"</strong> ?</p>
                <p class="text-danger"><i class="fas fa-warning me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ route('admin.projects.destroy', $project) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Supprimer définitivement
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-text {
    font-size: 0.8rem;
    margin-bottom: 0;
}
</style>
@endpush
