@extends('admin.layout')

@section('title', isset($project) ? 'Modifier Projet' : 'Nouveau Projet')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.projects.index') }}">Projects & Labs</a></li>
    <li class="breadcrumb-item active">{{ isset($project) ? 'Modifier Projet' : 'Nouveau Projet' }}</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">{{ isset($project) ? 'Modifier Projet' : 'Nouveau Projet' }}</h1>
        <p class="page-subtitle">{{ isset($project) ? 'Modifiez les informations du projet.' : 'C<PERSON>ez un nouveau projet, lab ou démonstration technique.' }}</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ route('admin.projects.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Retour à la liste
        </a>
    </div>
</div>

@if($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <strong>Erreurs de validation :</strong>
        <ul class="mb-0 mt-2">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>Informations du projet
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ isset($project) ? route('admin.projects.update', $project) : route('admin.projects.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @if(isset($project))
                        @method('PUT')
                    @endif
                    
                    <!-- Titre -->
                    <div class="mb-3">
                        <label for="title" class="form-label">Titre du projet *</label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror"
                               id="title" name="title" value="{{ old('title', $project->title ?? '') }}" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description" name="description" rows="4" required>{{ old('description', $project->description ?? '') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Type et Catégorie -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Type *</label>
                                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                    <option value="">Sélectionner un type</option>
                                    <option value="project" {{ old('type', $project->type ?? '') === 'project' ? 'selected' : '' }}>Projet</option>
                                    <option value="lab" {{ old('type', $project->type ?? '') === 'lab' ? 'selected' : '' }}>Lab</option>
                                    <option value="demo" {{ old('type', $project->type ?? '') === 'demo' ? 'selected' : '' }}>Démonstration</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">Catégorie</label>
                                <input type="text" class="form-control @error('category') is-invalid @enderror"
                                       id="category" name="category" value="{{ old('category', $project->category ?? '') }}"
                                       placeholder="ex: DevOps, Cloud, Security">
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- URLs -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="project_url" class="form-label">URL du projet</label>
                                <input type="url" class="form-control @error('project_url') is-invalid @enderror" 
                                       id="project_url" name="project_url" value="{{ old('project_url') }}" 
                                       placeholder="https://example.com">
                                @error('project_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="github_url" class="form-label">URL GitHub</label>
                                <input type="url" class="form-control @error('github_url') is-invalid @enderror" 
                                       id="github_url" name="github_url" value="{{ old('github_url') }}" 
                                       placeholder="https://github.com/user/repo">
                                @error('github_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="demo_url" class="form-label">URL Démo</label>
                                <input type="url" class="form-control @error('demo_url') is-invalid @enderror" 
                                       id="demo_url" name="demo_url" value="{{ old('demo_url') }}" 
                                       placeholder="https://demo.example.com">
                                @error('demo_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Image -->
                    <div class="mb-3">
                        <label for="image" class="form-label">Image du projet</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        <div class="form-text">Formats acceptés : JPEG, PNG, JPG, GIF. Taille max : 2MB</div>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Technologies -->
                    <div class="mb-3">
                        <label for="technologies" class="form-label">Technologies utilisées</label>
                        <input type="text" class="form-control" id="technologies-input" 
                               placeholder="Tapez une technologie et appuyez sur Entrée">
                        <div id="technologies-container" class="mt-2"></div>
                        <div class="form-text">Ajoutez les technologies une par une en appuyant sur Entrée</div>
                    </div>

                    <!-- Statut et Dates -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="status" class="form-label">Statut *</label>
                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                    <option value="">Sélectionner un statut</option>
                                    <option value="completed" {{ old('status') === 'completed' ? 'selected' : '' }}>Terminé</option>
                                    <option value="in_progress" {{ old('status') === 'in_progress' ? 'selected' : '' }}>En cours</option>
                                    <option value="planned" {{ old('status') === 'planned' ? 'selected' : '' }}>Planifié</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Date de début</label>
                                <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                       id="start_date" name="start_date" value="{{ old('start_date') }}">
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">Date de fin</label>
                                <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                       id="end_date" name="end_date" value="{{ old('end_date') }}">
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Options -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                           {{ old('is_featured') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        <i class="fas fa-star text-warning me-1"></i>Projet mis en avant
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="fas fa-eye text-success me-1"></i>Projet actif
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="order" class="form-label">Ordre d'affichage</label>
                                <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                       id="order" name="order" value="{{ old('order', 0) }}" min="0">
                                @error('order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.projects.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Créer le projet
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Aide
                </h6>
            </div>
            <div class="card-body">
                <h6>Types de projets :</h6>
                <ul class="small">
                    <li><strong>Projet :</strong> Projet complet avec objectifs définis</li>
                    <li><strong>Lab :</strong> Expérimentation ou test technique</li>
                    <li><strong>Démo :</strong> Démonstration de concept</li>
                </ul>

                <h6 class="mt-3">Statuts :</h6>
                <ul class="small">
                    <li><strong>Terminé :</strong> Projet achevé</li>
                    <li><strong>En cours :</strong> Développement actif</li>
                    <li><strong>Planifié :</strong> À venir</li>
                </ul>

                <h6 class="mt-3">Conseils :</h6>
                <ul class="small">
                    <li>Utilisez une image représentative du projet</li>
                    <li>Listez les technologies principales</li>
                    <li>Ajoutez des liens vers GitHub et démos</li>
                    <li>Les projets mis en avant apparaissent en premier</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Gestion des technologies
let technologies = @json(old('technologies', []));

function updateTechnologiesDisplay() {
    const container = document.getElementById('technologies-container');
    container.innerHTML = '';
    
    technologies.forEach((tech, index) => {
        const badge = document.createElement('span');
        badge.className = 'badge bg-primary me-2 mb-2';
        badge.innerHTML = `
            ${tech}
            <button type="button" class="btn-close btn-close-white ms-2" onclick="removeTechnology(${index})"></button>
            <input type="hidden" name="technologies[]" value="${tech}">
        `;
        container.appendChild(badge);
    });
}

function addTechnology() {
    const input = document.getElementById('technologies-input');
    const tech = input.value.trim();
    
    if (tech && !technologies.includes(tech)) {
        technologies.push(tech);
        updateTechnologiesDisplay();
        input.value = '';
    }
}

function removeTechnology(index) {
    technologies.splice(index, 1);
    updateTechnologiesDisplay();
}

// Event listeners
document.getElementById('technologies-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        addTechnology();
    }
});

// Initialiser l'affichage
updateTechnologiesDisplay();

// Validation des dates
document.getElementById('start_date').addEventListener('change', function() {
    const startDate = this.value;
    const endDateInput = document.getElementById('end_date');
    
    if (startDate) {
        endDateInput.min = startDate;
    }
});

document.getElementById('end_date').addEventListener('change', function() {
    const endDate = this.value;
    const startDateInput = document.getElementById('start_date');
    
    if (endDate) {
        startDateInput.max = endDate;
    }
});
</script>
@endpush
