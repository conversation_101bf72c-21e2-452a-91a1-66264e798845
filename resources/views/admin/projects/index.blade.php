@extends('admin.layout')

@section('title', 'Projects & Labs Management')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Projects & Labs</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Projects & Labs Management</h1>
        <p class="page-subtitle">Gérez vos projets, labs et démonstrations techniques.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.projects.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Nouveau Projet
            </a>
        </div>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <select class="form-select" id="typeFilter">
                    <option value="">Tous les types</option>
                    <option value="project">Projets</option>
                    <option value="lab">Labs</option>
                    <option value="demo">Démonstrations</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">Tous les statuts</option>
                    <option value="completed">Terminé</option>
                    <option value="in_progress">En cours</option>
                    <option value="planned">Planifié</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="featuredFilter">
                    <option value="">Tous</option>
                    <option value="1">Mis en avant</option>
                    <option value="0">Standard</option>
                </select>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="searchFilter" placeholder="Rechercher...">
            </div>
        </div>
    </div>
</div>

<div class="row" id="projectsContainer">
    @forelse($projects as $project)
        <div class="col-lg-4 col-md-6 mb-4 project-card" 
             data-type="{{ $project->type }}" 
             data-status="{{ $project->status }}" 
             data-featured="{{ $project->is_featured ? '1' : '0' }}"
             data-search="{{ strtolower($project->title . ' ' . $project->description . ' ' . $project->category) }}">
            <div class="card h-100 shadow-sm">
                <div class="position-relative">
                    <img src="{{ $project->image_url }}" class="card-img-top" alt="{{ $project->title }}" 
                         style="height: 200px; object-fit: cover;">
                    
                    <!-- Type Badge -->
                    <span class="position-absolute top-0 start-0 m-2">
                        <span class="badge bg-{{ $project->type_badge['class'] }}">
                            <i class="fas fa-{{ $project->type === 'project' ? 'folder' : ($project->type === 'lab' ? 'flask' : 'desktop') }} me-1"></i>
                            {{ $project->type_badge['text'] }}
                        </span>
                    </span>
                    
                    <!-- Status & Featured Badges -->
                    <div class="position-absolute top-0 end-0 m-2">
                        @if($project->is_featured)
                            <span class="badge bg-warning text-dark mb-1 d-block">
                                <i class="fas fa-star me-1"></i>Mis en avant
                            </span>
                        @endif
                        <span class="badge bg-{{ $project->status_badge['class'] }}">
                            <i class="fas fa-{{ $project->status === 'completed' ? 'check' : ($project->status === 'in_progress' ? 'clock' : 'calendar') }} me-1"></i>
                            {{ $project->status_badge['text'] }}
                        </span>
                    </div>
                    
                    <!-- Active Status -->
                    @if(!$project->is_active)
                        <div class="position-absolute bottom-0 start-0 end-0 bg-dark bg-opacity-75 text-white text-center py-1">
                            <small><i class="fas fa-eye-slash me-1"></i>Inactif</small>
                        </div>
                    @endif
                </div>
                
                <div class="card-body">
                    <h5 class="card-title">{{ $project->title }}</h5>
                    @if($project->category)
                        <span class="badge bg-secondary mb-2">{{ $project->category }}</span>
                    @endif
                    <p class="card-text text-muted small">
                        {{ Str::limit($project->description, 100) }}
                    </p>
                    
                    @if($project->technologies && count($project->technologies) > 0)
                        <div class="mb-2">
                            @foreach(array_slice($project->technologies, 0, 3) as $tech)
                                <span class="badge bg-light text-dark me-1">{{ $tech }}</span>
                            @endforeach
                            @if(count($project->technologies) > 3)
                                <span class="badge bg-light text-dark">+{{ count($project->technologies) - 3 }}</span>
                            @endif
                        </div>
                    @endif
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            {{ $project->duration }}
                        </small>
                        <div class="btn-group" role="group">
                            @if($project->project_url)
                                <a href="{{ $project->project_url }}" target="_blank" 
                                   class="btn btn-outline-success btn-sm" data-bs-toggle="tooltip" title="Voir le projet">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            @endif
                            @if($project->github_url)
                                <a href="{{ $project->github_url }}" target="_blank" 
                                   class="btn btn-outline-dark btn-sm" data-bs-toggle="tooltip" title="GitHub">
                                    <i class="fab fa-github"></i>
                                </a>
                            @endif
                            <a href="{{ route('admin.projects.show', $project) }}" 
                               class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" title="Détails">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ route('admin.projects.edit', $project) }}" 
                               class="btn btn-outline-warning btn-sm" data-bs-toggle="tooltip" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ route('admin.projects.destroy', $project) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger btn-sm" 
                                        data-bs-toggle="tooltip" title="Supprimer"
                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-folder-open fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">Aucun projet trouvé</h4>
                    <p class="text-muted mb-4">Commencez par ajouter votre premier projet ou lab.</p>
                    <a href="{{ route('admin.projects.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nouveau Projet
                    </a>
                </div>
            </div>
        </div>
    @endforelse
</div>

@if($projects->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Statistiques des projets
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <h4 class="text-primary">{{ $projects->count() }}</h4>
                            <small class="text-muted">Total</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-success">{{ $projects->where('type', 'project')->count() }}</h4>
                            <small class="text-muted">Projets</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-warning">{{ $projects->where('type', 'lab')->count() }}</h4>
                            <small class="text-muted">Labs</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-info">{{ $projects->where('type', 'demo')->count() }}</h4>
                            <small class="text-muted">Démonstrations</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-success">{{ $projects->where('status', 'completed')->count() }}</h4>
                            <small class="text-muted">Terminés</small>
                        </div>
                        <div class="col-md-2">
                            <h4 class="text-warning">{{ $projects->where('is_featured', true)->count() }}</h4>
                            <small class="text-muted">Mis en avant</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
@endsection

@push('scripts')
<script>
// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});

// Filtres
document.getElementById('typeFilter').addEventListener('change', filterProjects);
document.getElementById('statusFilter').addEventListener('change', filterProjects);
document.getElementById('featuredFilter').addEventListener('change', filterProjects);
document.getElementById('searchFilter').addEventListener('input', filterProjects);

function filterProjects() {
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const featuredFilter = document.getElementById('featuredFilter').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
    
    const projectCards = document.querySelectorAll('.project-card');
    
    projectCards.forEach(card => {
        const type = card.dataset.type;
        const status = card.dataset.status;
        const featured = card.dataset.featured;
        const searchText = card.dataset.search;
        
        let show = true;
        
        if (typeFilter && type !== typeFilter) show = false;
        if (statusFilter && status !== statusFilter) show = false;
        if (featuredFilter && featured !== featuredFilter) show = false;
        if (searchFilter && !searchText.includes(searchFilter)) show = false;
        
        card.style.display = show ? 'block' : 'none';
    });
}
</script>
@endpush
