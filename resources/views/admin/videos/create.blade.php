@extends('admin.layout')

@section('title', 'Add New Video')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.videos.index') }}">Videos</a></li>
    <li class="breadcrumb-item active">Add New Video</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Ajouter une nouvelle vidéo</h1>
        <p class="page-subtitle">Ajoutez une vidéo YouTube, Vimeo ou uploadez un fichier local.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.videos.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour aux vidéos
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-video me-2"></i>
                    Informations de la vidéo
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.videos.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">Titre de la vidéo *</label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required
                                   placeholder="e.g., Ma présentation professionnelle">
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="video_type" class="form-label">Type de vidéo *</label>
                            <select class="form-select @error('video_type') is-invalid @enderror" 
                                    id="video_type" name="video_type" required onchange="toggleVideoFields()">
                                <option value="">Sélectionner...</option>
                                <option value="youtube" {{ old('video_type') === 'youtube' ? 'selected' : '' }}>YouTube</option>
                                <option value="vimeo" {{ old('video_type') === 'vimeo' ? 'selected' : '' }}>Vimeo</option>
                                <option value="upload" {{ old('video_type') === 'upload' ? 'selected' : '' }}>Upload local</option>
                            </select>
                            @error('video_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3"
                                  placeholder="Description de la vidéo...">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- URL Field (YouTube/Vimeo) -->
                    <div class="mb-3" id="url-field" style="display: none;">
                        <label for="video_url" class="form-label">URL de la vidéo *</label>
                        <input type="url" class="form-control @error('video_url') is-invalid @enderror" 
                               id="video_url" name="video_url" value="{{ old('video_url') }}"
                               placeholder="https://www.youtube.com/watch?v=... ou https://vimeo.com/...">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Formats supportés: YouTube (watch, embed, youtu.be) et Vimeo
                        </small>
                        @error('video_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Upload Field -->
                    <div class="mb-3" id="upload-field" style="display: none;">
                        <label for="video_file" class="form-label">Fichier vidéo *</label>
                        <input type="file" class="form-control @error('video_file') is-invalid @enderror" 
                               id="video_file" name="video_file" accept="video/*">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Formats supportés: MP4, AVI, MOV, WMV (max 100MB)
                        </small>
                        @error('video_file')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="thumbnail" class="form-label">Thumbnail personnalisé</label>
                            <input type="file" class="form-control @error('thumbnail') is-invalid @enderror" 
                                   id="thumbnail" name="thumbnail" accept="image/*">
                            <small class="text-muted">
                                Optionnel. Si non fourni, le thumbnail sera généré automatiquement.
                            </small>
                            @error('thumbnail')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="duration" class="form-label">Durée (secondes)</label>
                            <input type="number" class="form-control @error('duration') is-invalid @enderror" 
                                   id="duration" name="duration" value="{{ old('duration') }}" min="1"
                                   placeholder="120">
                            <small class="text-muted">Optionnel</small>
                            @error('duration')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="order" class="form-label">Ordre d'affichage</label>
                            <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                   id="order" name="order" value="{{ old('order', 0) }}" min="0">
                            <small class="text-muted">0 = premier</small>
                            @error('order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    <strong>Vidéo active</strong>
                                    <small class="text-muted d-block">La vidéo sera visible sur le site</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_presentation" name="is_presentation" 
                                       value="1" {{ old('is_presentation') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_presentation">
                                    <strong>Vidéo de présentation</strong>
                                    <small class="text-muted d-block">Sera utilisée comme vidéo principale</small>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Créer la vidéo
                        </button>
                        <a href="{{ route('admin.videos.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Guide d'utilisation
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6 class="text-primary">Types de vidéos supportés</h6>
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fab fa-youtube text-danger me-2"></i>
                            <strong>YouTube:</strong> Copiez l'URL de la vidéo
                        </li>
                        <li class="mb-2">
                            <i class="fab fa-vimeo text-info me-2"></i>
                            <strong>Vimeo:</strong> Copiez l'URL de la vidéo
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-upload text-success me-2"></i>
                            <strong>Upload:</strong> Fichier local (max 100MB)
                        </li>
                    </ul>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary">Formats supportés</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>MP4 (recommandé)</li>
                        <li><i class="fas fa-check text-success me-2"></i>AVI</li>
                        <li><i class="fas fa-check text-success me-2"></i>MOV</li>
                        <li><i class="fas fa-check text-success me-2"></i>WMV</li>
                    </ul>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary">Conseils</h6>
                    <ul class="list-unstyled small">
                        <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>Utilisez un titre descriptif</li>
                        <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>Ajoutez une description détaillée</li>
                        <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>Une seule vidéo de présentation à la fois</li>
                        <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>Optimisez la taille pour le web</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-video me-2"></i>
                    Exemples d'URLs
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>YouTube:</strong>
                    <code class="d-block small mt-1">https://www.youtube.com/watch?v=dQw4w9WgXcQ</code>
                    <code class="d-block small">https://youtu.be/dQw4w9WgXcQ</code>
                </div>
                <div class="mb-3">
                    <strong>Vimeo:</strong>
                    <code class="d-block small mt-1">https://vimeo.com/123456789</code>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleVideoFields() {
    const videoType = document.getElementById('video_type').value;
    const urlField = document.getElementById('url-field');
    const uploadField = document.getElementById('upload-field');
    const videoUrlInput = document.getElementById('video_url');
    const videoFileInput = document.getElementById('video_file');
    
    // Hide all fields first
    urlField.style.display = 'none';
    uploadField.style.display = 'none';
    
    // Remove required attributes
    videoUrlInput.removeAttribute('required');
    videoFileInput.removeAttribute('required');
    
    // Show appropriate field based on selection
    if (videoType === 'youtube' || videoType === 'vimeo') {
        urlField.style.display = 'block';
        videoUrlInput.setAttribute('required', 'required');
    } else if (videoType === 'upload') {
        uploadField.style.display = 'block';
        videoFileInput.setAttribute('required', 'required');
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleVideoFields();
});

// Preview thumbnail
document.getElementById('thumbnail').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // You can add thumbnail preview here if needed
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endpush
