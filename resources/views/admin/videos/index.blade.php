@extends('admin.layout')

@section('title', 'Videos Management')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Videos</li>
@endsection

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div>
        <h1 class="page-title">Videos Management</h1>
        <p class="page-subtitle">Gérez vos vidéos de présentation et contenus multimédias.</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.videos.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Ajouter une vidéo
            </a>
        </div>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<div class="row">
    @forelse($videos as $video)
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="position-relative">
                    <img src="{{ $video->thumbnail_url }}" class="card-img-top" alt="{{ $video->title }}" 
                         style="height: 200px; object-fit: cover;">
                    
                    <!-- Video Type Badge -->
                    <span class="position-absolute top-0 start-0 m-2">
                        <span class="badge bg-{{ $video->video_type === 'youtube' ? 'danger' : ($video->video_type === 'vimeo' ? 'info' : 'success') }}">
                            <i class="fab fa-{{ $video->video_type === 'upload' ? 'video' : $video->video_type }} me-1"></i>
                            {{ ucfirst($video->video_type) }}
                        </span>
                    </span>
                    
                    <!-- Status Badges -->
                    <div class="position-absolute top-0 end-0 m-2">
                        @if($video->is_presentation)
                            <span class="badge bg-warning text-dark mb-1 d-block">
                                <i class="fas fa-star me-1"></i>Présentation
                            </span>
                        @endif
                        <span class="badge bg-{{ $video->is_active ? 'success' : 'secondary' }}">
                            <i class="fas fa-{{ $video->is_active ? 'eye' : 'eye-slash' }} me-1"></i>
                            {{ $video->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    
                    <!-- Play Button Overlay -->
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <button class="btn btn-primary btn-lg rounded-circle" 
                                data-bs-toggle="modal" 
                                data-bs-target="#videoModal{{ $video->id }}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    
                    <!-- Duration -->
                    @if($video->duration)
                        <span class="position-absolute bottom-0 end-0 m-2">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="fas fa-clock me-1"></i>{{ $video->duration_formatted }}
                            </span>
                        </span>
                    @endif
                </div>
                
                <div class="card-body">
                    <h5 class="card-title">{{ $video->title }}</h5>
                    @if($video->description)
                        <p class="card-text text-muted small">
                            {{ Str::limit($video->description, 100) }}
                        </p>
                    @endif
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            {{ $video->created_at->format('d/m/Y') }}
                        </small>
                        <div class="btn-group" role="group">
                            <a href="{{ route('admin.videos.show', $video) }}" 
                               class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" title="Voir">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ route('admin.videos.edit', $video) }}" 
                               class="btn btn-outline-warning btn-sm" data-bs-toggle="tooltip" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ route('admin.videos.destroy', $video) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger btn-sm" 
                                        data-bs-toggle="tooltip" title="Supprimer"
                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette vidéo ?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Video Modal -->
        <div class="modal fade" id="videoModal{{ $video->id }}" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ $video->title }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-0">
                        @if($video->video_type === 'upload')
                            <video controls class="w-100" style="max-height: 400px;">
                                <source src="{{ $video->embed_url }}" type="video/mp4">
                                Votre navigateur ne supporte pas la lecture vidéo.
                            </video>
                        @else
                            <div class="ratio ratio-16x9">
                                <iframe src="{{ $video->embed_url }}" 
                                        title="{{ $video->title }}" 
                                        frameborder="0" 
                                        allowfullscreen>
                                </iframe>
                            </div>
                        @endif
                    </div>
                    @if($video->description)
                        <div class="modal-footer">
                            <p class="text-muted mb-0">{{ $video->description }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-video fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">Aucune vidéo trouvée</h4>
                    <p class="text-muted mb-4">Commencez par ajouter votre première vidéo de présentation.</p>
                    <a href="{{ route('admin.videos.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Ajouter une vidéo
                    </a>
                </div>
            </div>
        </div>
    @endforelse
</div>

@if($videos->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Statistiques des vidéos
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">{{ $videos->count() }}</h4>
                            <small class="text-muted">Total vidéos</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">{{ $videos->where('is_active', true)->count() }}</h4>
                            <small class="text-muted">Actives</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">{{ $videos->where('is_presentation', true)->count() }}</h4>
                            <small class="text-muted">Présentation</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">{{ $videos->where('video_type', 'upload')->count() }}</h4>
                            <small class="text-muted">Uploadées</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
@endsection

@push('scripts')
<script>
// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});

// Stop video when modal is closed
document.querySelectorAll('[id^="videoModal"]').forEach(function(modal) {
    modal.addEventListener('hidden.bs.modal', function () {
        const iframe = this.querySelector('iframe');
        const video = this.querySelector('video');
        
        if (iframe) {
            const src = iframe.src;
            iframe.src = '';
            iframe.src = src;
        }
        
        if (video) {
            video.pause();
            video.currentTime = 0;
        }
    });
});
</script>
@endpush
