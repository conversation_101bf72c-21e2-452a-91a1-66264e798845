<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $profile->name ?? 'Portfolio' }} - DevOps Engineer</title>
    <style>
        /* Modern Portfolio - Standalone Version */
        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --dark: #0f172a;
            --light: #f8fafc;
            --text: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text);
            overflow-x: hidden;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1rem 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            text-decoration: none;
            color: var(--text);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--dark) 0%, #1e293b 100%);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-content h1 {
            font-size: clamp(2.5rem, 8vw, 4rem);
            font-weight: 800;
            color: white;
            margin-bottom: 1rem;
        }

        .hero-content .gradient-text {
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-content h2 {
            font-size: 1.5rem;
            color: var(--accent);
            margin-bottom: 1rem;
        }

        .hero-content p {
            font-size: 1.1rem;
            color: #94a3b8;
            margin-bottom: 2rem;
            max-width: 500px;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .btn {
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .btn-outline {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-outline:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .hero-avatar {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .avatar-circle {
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 6rem;
            color: white;
            position: relative;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Sections */
        .section {
            padding: 5rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title span {
            color: var(--primary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            font-size: 0.9rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-top: 0.5rem;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 2rem;
        }

        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

        /* Cards */
        .card {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
        }

        /* Skills */
        .skill-item {
            margin-bottom: 1.5rem;
        }

        .skill-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .skill-bar {
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 4px;
            transition: width 2s ease;
        }

        /* Contact */
        .contact {
            background: linear-gradient(135deg, var(--dark), #1e293b);
            color: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.2rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .contact-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                display: none;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Utilities */
        .bg-light { background: var(--light); }
        .text-center { text-align: center; }
        .mb-4 { margin-bottom: 2rem; }
        .mt-4 { margin-top: 2rem; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">{{ $profile->name ?? 'Portfolio' }}</div>
            <ul class="nav-links">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
                <li><a href="#experience" class="nav-link">Experience</a></li>
                <li><a href="#skills" class="nav-link">Skills</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    @if($profile)
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1>Hello, I'm <span class="gradient-text">{{ $profile->name }}</span></h1>
                <h2>{{ $profile->title }}</h2>
                <p>{{ $profile->description }}</p>
                
                <div class="hero-buttons">
                    @if($profile->cv_url)
                        <a href="{{ $profile->cv_url }}" target="_blank" class="btn btn-primary">Download CV</a>
                    @endif
                    <a href="#contact" class="btn btn-outline">Let's Talk</a>
                </div>
            </div>
            
            <div class="hero-avatar">
                <div class="avatar-circle">
                    👨‍💻
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Experience Section -->
    @if($experiences && $experiences->count() > 0)
    <section id="experience" class="section">
        <div class="container">
            <div class="section-title">
                <span>Experience</span>
                <h2>Professional Journey</h2>
            </div>

            <div class="grid grid-1">
                @foreach($experiences as $experience)
                <div class="card">
                    <h3>{{ $experience->position }}</h3>
                    <h4 style="color: var(--primary); margin-bottom: 1rem;">{{ $experience->company }}</h4>
                    <p style="color: var(--text); margin-bottom: 1rem;">{{ $experience->date_range }}</p>
                    <p style="margin-bottom: 1rem;">{{ $experience->description }}</p>

                    @if($experience->responsibilities && count($experience->responsibilities) > 0)
                        <ul style="margin-left: 1rem;">
                            @foreach($experience->responsibilities as $responsibility)
                                <li style="margin-bottom: 0.5rem;">{{ $responsibility }}</li>
                            @endforeach
                        </ul>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Skills Section -->
    @if($skills && $skills->count() > 0)
    <section id="skills" class="section bg-light">
        <div class="container">
            <div class="section-title">
                <span>Skills</span>
                <h2>Technical Expertise</h2>
            </div>

            <div class="grid grid-2">
                @foreach($skills as $skill)
                <div class="skill-item">
                    <div class="skill-header">
                        <span style="font-weight: 600;">{{ $skill->name }}</span>
                        <span style="color: var(--primary); font-weight: 600;">{{ $skill->level }}%</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: {{ $skill->level }}%"></div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Education Section -->
    @if($educations && $educations->count() > 0)
    <section class="section">
        <div class="container">
            <div class="section-title">
                <span>Education</span>
                <h2>Academic Background</h2>
            </div>

            <div class="grid grid-2">
                @foreach($educations as $education)
                <div class="card">
                    <h4>{{ $education->degree }}</h4>
                    @if($education->field_of_study)
                        <h5 style="color: var(--primary); margin-bottom: 1rem;">{{ $education->field_of_study }}</h5>
                    @endif
                    <p style="font-weight: 600; margin-bottom: 0.5rem;">{{ $education->institution }}</p>
                    <p style="color: var(--text); margin-bottom: 1rem;">{{ $education->date_range }}</p>
                    @if($education->description)
                        <p>{{ $education->description }}</p>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Contact Section -->
    @if($profile)
    <section id="contact" class="section contact">
        <div class="container">
            <div class="section-title">
                <span style="color: rgba(255,255,255,0.7);">Contact</span>
                <h2 style="color: white;">Let's Work Together</h2>
            </div>

            <div class="contact-grid">
                <div>
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div>
                            <h6 style="margin-bottom: 0.5rem;">Email</h6>
                            <a href="mailto:{{ $profile->email }}" style="color: rgba(255,255,255,0.8); text-decoration: none;">{{ $profile->email }}</a>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📱</div>
                        <div>
                            <h6 style="margin-bottom: 0.5rem;">Phone</h6>
                            <span style="color: rgba(255,255,255,0.8);">{{ $profile->phone }}</span>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div>
                            <h6 style="margin-bottom: 0.5rem;">Location</h6>
                            <span style="color: rgba(255,255,255,0.8);">{{ $profile->address }}</span>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <h4 style="color: white; margin-bottom: 2rem;">Quick Stats</h4>
                    <div class="grid grid-2" style="gap: 1rem;">
                        <div style="text-align: center;">
                            <h2 style="color: white; margin-bottom: 0.5rem;">{{ $experiences->count() }}</h2>
                            <p style="color: rgba(255,255,255,0.7);">Years Experience</p>
                        </div>
                        <div style="text-align: center;">
                            <h2 style="color: white; margin-bottom: 0.5rem;">{{ $skills->count() }}</h2>
                            <p style="color: rgba(255,255,255,0.7);">Technical Skills</p>
                        </div>
                        <div style="text-align: center;">
                            <h2 style="color: white; margin-bottom: 0.5rem;">{{ $certifications->count() }}</h2>
                            <p style="color: rgba(255,255,255,0.7);">Certifications</p>
                        </div>
                        <div style="text-align: center;">
                            <h2 style="color: white; margin-bottom: 0.5rem;">50+</h2>
                            <p style="color: rgba(255,255,255,0.7);">Projects</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Footer -->
    <footer style="background: var(--dark); color: white; padding: 2rem 0; text-align: center;">
        <div class="container">
            <p>&copy; {{ date('Y') }} {{ $profile->name ?? 'Portfolio' }}. All rights reserved.</p>
            <p style="margin-top: 0.5rem; opacity: 0.7; font-size: 0.9rem;">Built with ❤️ using Laravel</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Animate skill bars
        window.addEventListener('load', function() {
            const skillBars = document.querySelectorAll('.skill-progress');
            skillBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 1000);
            });
        });
    </script>
</body>
</html>
