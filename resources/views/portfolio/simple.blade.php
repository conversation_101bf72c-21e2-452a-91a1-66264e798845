<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $profile->name ?? 'Portfolio' }} - DevOps Engineer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .hero {
            text-align: center;
            color: white;
            padding: 100px 0;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .hero h2 {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .hero p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            opacity: 0.8;
        }

        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            margin: 0 10px;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .section {
            background: white;
            margin: 50px 0;
            padding: 50px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #333;
            text-align: center;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .card {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .card h5 {
            color: #333;
            margin-bottom: 5px;
        }

        .card p {
            color: #666;
            margin-bottom: 10px;
        }

        .skill-item {
            margin-bottom: 20px;
        }

        .skill-name {
            display: flex;
            justify-content: between;
            margin-bottom: 5px;
        }

        .skill-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }

        .skill-progress {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            border-radius: 4px;
            transition: width 2s ease;
        }

        .contact {
            background: #333;
            color: white;
            text-align: center;
            padding: 50px;
            border-radius: 20px;
        }

        .contact h3 {
            color: white;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 50px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .contact-item {
            text-align: center;
        }

        .contact-item strong {
            display: block;
            margin-bottom: 5px;
        }

        .social-links {
            margin-top: 30px;
        }

        .social-links a {
            color: white;
            font-size: 1.5rem;
            margin: 0 15px;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 30px 20px;
                margin: 30px 0;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    @if($profile)
    <div class="hero">
        <div class="container">
            <h1>{{ $profile->name }}</h1>
            <h2>{{ $profile->title }}</h2>
            <p>{{ $profile->description }}</p>
            <div>
                @if($profile->cv_url)
                    <a href="{{ $profile->cv_url }}" target="_blank" class="btn">Download CV</a>
                @endif
                <a href="#contact" class="btn">Contact Me</a>
            </div>
        </div>
    </div>
    @endif

    <div class="container">
        <!-- Experience Section -->
        @if($experiences && $experiences->count() > 0)
        <div class="section">
            <h3>Professional Experience</h3>
            <div class="grid">
                @foreach($experiences as $experience)
                <div class="card">
                    <h4>{{ $experience->position }}</h4>
                    <h5>{{ $experience->company }}</h5>
                    @if($experience->location)
                        <p><strong>Location:</strong> {{ $experience->location }}</p>
                    @endif
                    <p><strong>Period:</strong> {{ $experience->date_range }}</p>
                    <p>{{ $experience->description }}</p>
                    @if($experience->responsibilities && count($experience->responsibilities) > 0)
                        <ul>
                            @foreach($experience->responsibilities as $responsibility)
                                <li>{{ $responsibility }}</li>
                            @endforeach
                        </ul>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Skills Section -->
        @if($skills && $skills->count() > 0)
        <div class="section">
            <h3>Technical Skills</h3>
            <div class="grid">
                @foreach($skills as $skill)
                <div class="skill-item">
                    <div class="skill-name">
                        <span><strong>{{ $skill->name }}</strong></span>
                        <span>{{ $skill->level }}%</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: {{ $skill->level }}%"></div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Education Section -->
        @if($educations && $educations->count() > 0)
        <div class="section">
            <h3>Education</h3>
            <div class="grid">
                @foreach($educations as $education)
                <div class="card">
                    <h4>{{ $education->degree }}</h4>
                    @if($education->field_of_study)
                        <h5>{{ $education->field_of_study }}</h5>
                    @endif
                    <p><strong>{{ $education->institution }}</strong></p>
                    @if($education->location)
                        <p>{{ $education->location }}</p>
                    @endif
                    <p><strong>Period:</strong> {{ $education->date_range }}</p>
                    @if($education->description)
                        <p>{{ $education->description }}</p>
                    @endif
                    @if($education->grade)
                        <p><strong>Grade:</strong> {{ $education->grade }}</p>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Certifications Section -->
        @if($certifications && $certifications->count() > 0)
        <div class="section">
            <h3>Certifications</h3>
            <div class="grid">
                @foreach($certifications as $certification)
                <div class="card">
                    <h4>{{ $certification->name }}</h4>
                    <h5>{{ $certification->issuing_organization }}</h5>
                    <p><strong>Year:</strong> {{ $certification->date_display }}</p>
                    @if($certification->description)
                        <p>{{ $certification->description }}</p>
                    @endif
                    @if($certification->credential_id)
                        <p><strong>ID:</strong> {{ $certification->credential_id }}</p>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Contact Section -->
        @if($profile)
        <div class="contact" id="contact">
            <h3>Get In Touch</h3>
            <div class="contact-info">
                <div class="contact-item">
                    <strong>Email</strong>
                    <a href="mailto:{{ $profile->email }}" style="color: white;">{{ $profile->email }}</a>
                </div>
                <div class="contact-item">
                    <strong>Phone</strong>
                    {{ $profile->phone }}
                </div>
                <div class="contact-item">
                    <strong>Location</strong>
                    {{ $profile->address }}
                </div>
            </div>
            
            <div class="social-links">
                @if($profile->linkedin)
                    <a href="{{ $profile->linkedin }}" target="_blank">LinkedIn</a>
                @endif
                @if($profile->github)
                    <a href="{{ $profile->github }}" target="_blank">GitHub</a>
                @endif
                @if($profile->website)
                    <a href="{{ $profile->website }}" target="_blank">Website</a>
                @endif
            </div>
        </div>
        @endif
    </div>

    <script>
        // Simple smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Animate skill bars on load
        window.addEventListener('load', function() {
            const skillBars = document.querySelectorAll('.skill-progress');
            skillBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>
