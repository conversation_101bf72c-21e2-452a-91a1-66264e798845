<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $profile->name ?? 'Portfolio' }} - <PERSON><PERSON>ps Engineer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --dark-color: #0f172a;
            --light-color: #f8fafc;
            --text-color: #334155;
            --text-light: #64748b;
            --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
            --gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            overflow-x: hidden;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-color);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        /* Navigation */
        .navbar-modern {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .navbar-modern.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-color) !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Hero Section */
        .hero-section {
            min-height: 100vh;
            background: var(--gradient-dark);
            background-image:
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="%236366f1" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>'),
                linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%236366f1" stop-opacity="0.1"/><stop offset="100%" stop-color="%236366f1" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        /* Tech Icons Floating Animation */
        .tech-icon {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--accent-color);
            font-size: 24px;
            backdrop-filter: blur(10px);
            animation: techFloat 15s ease-in-out infinite;
        }

        .tech-icon:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .tech-icon:nth-child(2) { top: 60%; left: 15%; animation-delay: 2s; }
        .tech-icon:nth-child(3) { top: 30%; right: 20%; animation-delay: 4s; }
        .tech-icon:nth-child(4) { top: 70%; right: 10%; animation-delay: 6s; }
        .tech-icon:nth-child(5) { top: 10%; left: 50%; animation-delay: 8s; }
        .tech-icon:nth-child(6) { bottom: 20%; left: 30%; animation-delay: 10s; }

        @keyframes techFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg) scale(1);
                opacity: 0.7;
            }
            25% {
                transform: translateY(-20px) rotate(90deg) scale(1.1);
                opacity: 1;
            }
            50% {
                transform: translateY(-10px) rotate(180deg) scale(0.9);
                opacity: 0.8;
            }
            75% {
                transform: translateY(-30px) rotate(270deg) scale(1.05);
                opacity: 0.9;
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            animation: heroContentSlide 1.2s ease-out;
        }

        @keyframes heroContentSlide {
            0% {
                opacity: 0;
                transform: translateX(-50px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .hero-title {
            font-size: clamp(2.5rem, 8vw, 5rem);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            animation: titleGlow 2s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
            }
            100% {
                text-shadow: 0 0 30px rgba(99, 102, 241, 0.6), 0 0 40px rgba(139, 92, 246, 0.3);
            }
        }

        .hero-title .gradient-text {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        .hero-subtitle {
            font-size: clamp(1.2rem, 3vw, 1.8rem);
            font-weight: 600;
            color: var(--accent-color);
            margin-bottom: 1rem;
            animation: subtitleFade 1.5s ease-out 0.3s both;
        }

        @keyframes subtitleFade {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-description {
            font-size: 1.2rem;
            color: #94a3b8;
            margin-bottom: 3rem;
            max-width: 600px;
            animation: descriptionFade 1.8s ease-out 0.6s both;
        }

        @keyframes descriptionFade {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .btn-modern {
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: none;
            font-size: 1.1rem;
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: buttonFloat 2s ease-in-out 0.9s both;
        }

        @keyframes buttonFloat {
            0% {
                opacity: 0;
                transform: translateY(40px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .btn-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .btn-primary-modern {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            color: white;
        }

        .btn-outline-modern {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-outline-modern:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            color: white;
        }

        .hero-avatar {
            position: relative;
            width: 300px;
            height: 300px;
            margin: 0 auto;
        }

        .hero-avatar::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: var(--gradient-primary);
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.05); opacity: 0.9; }
        }

        .hero-avatar-inner {
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8rem;
            color: white;
            box-shadow: var(--shadow-xl);
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 60%; right: 10%; animation-delay: 2s; }
        .floating-element:nth-child(3) { bottom: 20%; left: 20%; animation-delay: 4s; }

        .floating-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--accent-color);
            font-size: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Stats Section */
        .stats-section {
            background: white;
            padding: 4rem 0;
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }

        .stats-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-xl);
        }

        .stats-number {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
        }

        .stats-label {
            font-weight: 600;
            color: var(--text-light);
            margin-top: 0.5rem;
        }

        /* Contact Form Styles */
        .contact-form .form-control {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            transition: all 0.3s ease;
        }

        .contact-form .form-control:focus {
            background: rgba(255, 255, 255, 0.15) !important;
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25) !important;
            color: white !important;
        }

        .contact-form .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6) !important;
        }

        .contact-form .form-floating > label {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        .contact-form .form-floating > .form-control:focus ~ label,
        .contact-form .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: var(--primary-color) !important;
        }

        .backdrop-blur {
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Hero Background with IT Theme */
        .hero-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
            z-index: 1;
            animation: backgroundPulse 8s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% {
                opacity: 0.7;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
        }

        .hero-section::before {
            z-index: 0;
        }

        /* Particles Effect */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(99, 102, 241, 0.6);
            border-radius: 50%;
            animation: particleFloat 20s linear infinite;
        }

        .particle:nth-child(odd) {
            background: rgba(139, 92, 246, 0.6);
            animation-duration: 25s;
        }

        .particle:nth-child(3n) {
            background: rgba(6, 182, 212, 0.6);
            animation-duration: 30s;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Network Connection Lines */
        .network-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.4), transparent);
            animation: networkPulse 4s ease-in-out infinite;
        }

        @keyframes networkPulse {
            0%, 100% {
                opacity: 0.3;
                transform: scaleX(0.5);
            }
            50% {
                opacity: 1;
                transform: scaleX(1);
            }
        }

        /* Tech Categories Styles */
        @keyframes techPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .tech-category-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .tech-category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.6s;
        }

        .tech-category-card:hover::before {
            left: 100%;
        }

        .tech-category-card:hover {
            transform: translateY(-10px);
            border-color: rgba(99, 102, 241, 0.5);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .tech-category-header {
            position: relative;
            z-index: 2;
        }

        .tech-items {
            position: relative;
            z-index: 2;
        }

        .tech-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            color: #94a3b8;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .tech-item:last-child {
            border-bottom: none;
        }

        .tech-item:hover {
            color: #06b6d4;
            transform: translateX(10px);
        }

        .tech-item i {
            width: 24px;
            margin-right: 12px;
            color: #6366f1;
            transition: color 0.3s ease;
        }

        .tech-item:hover i {
            color: #06b6d4;
        }

        /* Tech Image Container */
        .tech-image-container {
            position: relative;
            display: inline-block;
            border-radius: 15px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            transition: all 0.4s ease;
        }

        .tech-image-container:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
        }

        .tech-image-container img {
            transition: all 0.4s ease;
            filter: brightness(1.2) contrast(1.1);
        }

        .tech-image-container:hover img {
            filter: brightness(1.4) contrast(1.2) saturate(1.2);
        }

        /* Responsive adjustments for tech section */
        @media (max-width: 768px) {
            .tech-category-card {
                margin-bottom: 2rem;
            }

            .tech-image-container {
                padding: 10px;
            }

            .tech-image-container img {
                width: 80px !important;
                height: 60px !important;
            }
        }

        /* Projects Section Styles */
        .project-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
        }

        .project-card:hover .project-image {
            transform: scale(1.05);
        }

        .project-overlay {
            background: rgba(0, 0, 0, 0.8);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .project-card:hover .project-overlay {
            opacity: 1;
        }

        #projectTabs .nav-link {
            background: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            margin: 0 5px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        #projectTabs .nav-link:hover,
        #projectTabs .nav-link.active {
            background: var(--primary-color);
            color: white;
        }

        .project-item {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .project-item.hidden {
            opacity: 0;
            transform: scale(0.8);
            pointer-events: none;
        }

        /* Language Dropdown Styles */
        #languageDropdown {
            cursor: pointer;
        }

        .dropdown-menu .dropdown-item {
            transition: background-color 0.3s ease;
        }

        .dropdown-menu .dropdown-item:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .dropdown-menu .dropdown-item.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <i class="fas fa-code me-2"></i>{{ $profile->name ?? 'Portfolio' }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">{{ __('portfolio.home') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">{{ __('portfolio.about') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#experience">{{ __('portfolio.experience') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#skills">{{ __('portfolio.skills') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#education">{{ __('portfolio.education') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#projects">{{ __('portfolio.projects') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">{{ __('portfolio.contact') }}</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-globe me-1"></i>
                            @if(app()->getLocale() === 'fr')
                                Français
                            @else
                                English
                            @endif
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                            <li>
                                <a class="dropdown-item {{ app()->getLocale() === 'en' ? 'active' : '' }}"
                                   href="{{ route('language.switch', 'en') }}">
                                    🇺🇸 English
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ app()->getLocale() === 'fr' ? 'active' : '' }}"
                                   href="{{ route('language.switch', 'fr') }}">
                                    🇫🇷 Français
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    @if($profile)
    <section id="home" class="hero-section">
        <!-- Particles Background -->
        <div class="particles">
            <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
            <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
            <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
            <div class="particle" style="left: 40%; animation-delay: 6s;"></div>
            <div class="particle" style="left: 50%; animation-delay: 8s;"></div>
            <div class="particle" style="left: 60%; animation-delay: 10s;"></div>
            <div class="particle" style="left: 70%; animation-delay: 12s;"></div>
            <div class="particle" style="left: 80%; animation-delay: 14s;"></div>
            <div class="particle" style="left: 90%; animation-delay: 16s;"></div>
        </div>

        <!-- Network Lines -->
        <div class="network-line" style="top: 20%; left: 10%; width: 200px; animation-delay: 0s;"></div>
        <div class="network-line" style="top: 60%; right: 15%; width: 150px; animation-delay: 1s;"></div>
        <div class="network-line" style="bottom: 30%; left: 30%; width: 180px; animation-delay: 2s;"></div>

        <!-- Tech Icons Floating -->
        <div class="tech-icon">
            <i class="fas fa-server"></i>
        </div>
        <div class="tech-icon">
            <i class="fas fa-network-wired"></i>
        </div>
        <div class="tech-icon">
            <i class="fas fa-cloud"></i>
        </div>
        <div class="tech-icon">
            <i class="fas fa-database"></i>
        </div>
        <div class="tech-icon">
            <i class="fas fa-shield-alt"></i>
        </div>
        <div class="tech-icon">
            <i class="fas fa-cogs"></i>
        </div>

        <!-- Floating Elements -->
        <div class="floating-element">
            <div class="floating-icon">
                <i class="fab fa-docker"></i>
            </div>
        </div>
        <div class="floating-element">
            <div class="floating-icon">
                <i class="fab fa-aws"></i>
            </div>
        </div>
        <div class="floating-element">
            <div class="floating-icon">
                <i class="fab fa-linux"></i>
            </div>
        </div>

        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-7 hero-content" data-aos="fade-right" data-aos-duration="1000">
                    <div class="mb-4">
                        <span class="badge bg-primary bg-opacity-20 text-primary px-3 py-2 rounded-pill">
                            <i class="fas fa-rocket me-2"></i>Available for Freelance
                        </span>
                    </div>
                    <h1 class="hero-title text-white">
                        {{ __('portfolio.hello') }} <span class="gradient-text">{{ $profile->name }}</span>
                    </h1>
                    <h2 class="hero-subtitle">{{ $profile->title ?? __('portfolio.profession') }}</h2>
                    <p class="hero-description">{{ $profile->description ?? __('portfolio.description') }}</p>

                    <div class="d-flex flex-wrap gap-3 mb-4">
                        <a href="{{ asset('cv/CV_Tsiky.pdf') }}" target="_blank" class="btn btn-primary-modern">
                            <i class="fas fa-download me-2"></i>{{ __('portfolio.download_cv') }}
                        </a>
                        <a href="#contact" class="btn btn-outline-modern">
                            <i class="fas fa-envelope me-2"></i>{{ __('portfolio.lets_talk') }}
                        </a>
                        @if(isset($presentationVideo) && $presentationVideo)
                            <button type="button" class="btn btn-outline-modern" data-bs-toggle="modal" data-bs-target="#presentationModal">
                                <i class="fas fa-play me-2"></i>{{ __('portfolio.presentation_button') }}
                            </button>
                        @endif
                    </div>

                    <!-- Social Links -->
                    <div class="d-flex gap-3">
                        @if($profile->linkedin)
                            <a href="{{ $profile->linkedin }}" target="_blank" class="text-white fs-4" data-bs-toggle="tooltip" title="LinkedIn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        @endif
                        @if($profile->github)
                            <a href="{{ $profile->github }}" target="_blank" class="text-white fs-4" data-bs-toggle="tooltip" title="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                        @endif
                        @if($profile->website)
                            <a href="{{ $profile->website }}" target="_blank" class="text-white fs-4" data-bs-toggle="tooltip" title="Website">
                                <i class="fas fa-globe"></i>
                            </a>
                        @endif
                        <a href="mailto:{{ $profile->email }}" class="text-white fs-4" data-bs-toggle="tooltip" title="Email">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-5 text-center" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                    <div class="hero-avatar">
                        <div class="hero-avatar-inner">
                            <img src="{{ asset('images/tsiky.jpeg') }}" alt="{{ $profile->name ?? 'Tsiky Nitokiana' }}"
                                 style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Technologies Showcase Section -->
    <section class="py-5" style="background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%); position: relative; overflow: hidden;">
        <!-- Animated Background -->
        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0.1;">
            <div style="position: absolute; top: 20%; left: 10%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%); border-radius: 50%; animation: techPulse 4s ease-in-out infinite;"></div>
            <div style="position: absolute; top: 60%; right: 15%; width: 150px; height: 150px; background: radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, transparent 70%); border-radius: 50%; animation: techPulse 4s ease-in-out infinite 2s;"></div>
            <div style="position: absolute; bottom: 20%; left: 30%; width: 180px; height: 180px; background: radial-gradient(circle, rgba(6, 182, 212, 0.3) 0%, transparent 70%); border-radius: 50%; animation: techPulse 4s ease-in-out infinite 1s;"></div>
        </div>

        <div class="container" style="position: relative; z-index: 2;">
            <div class="text-center mb-5" data-aos="fade-up">
                <span class="text-primary fw-bold text-uppercase tracking-wider" style="color: #06b6d4 !important;">Technologies</span>
                <h2 class="display-5 fw-bold mt-2 text-white">Network & System Expertise</h2>
                <p class="lead" style="color: #94a3b8;">Technologies I master for infrastructure and development</p>
            </div>

            <div class="row g-4">
                <!-- Network Technologies -->
                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="tech-category-card">
                        <div class="tech-category-header">
                            <div class="tech-image-container mb-3">
                                <img src="{{ asset('images/network-tech.svg') }}" alt="Network Technologies" style="width: 100px; height: 75px; object-fit: contain;">
                            </div>
                            <h4 class="text-white mb-3">Network Technologies</h4>
                        </div>
                        <div class="tech-items">
                            <div class="tech-item">
                                <i class="fas fa-router"></i>
                                <span>Cisco Networking</span>
                            </div>
                            <div class="tech-item">
                                <i class="fas fa-wifi"></i>
                                <span>Mikrotik</span>
                            </div>
                            <div class="tech-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Firewall Management</span>
                            </div>
                            <div class="tech-item">
                                <i class="fas fa-sitemap"></i>
                                <span>VLAN Configuration</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Administration -->
                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="tech-category-card">
                        <div class="tech-category-header">
                            <div class="tech-image-container mb-3">
                                <img src="{{ asset('images/system-admin.svg') }}" alt="System Administration" style="width: 100px; height: 75px; object-fit: contain;">
                            </div>
                            <h4 class="text-white mb-3">System Administration</h4>
                        </div>
                        <div class="tech-items">
                            <div class="tech-item">
                                <i class="fab fa-linux"></i>
                                <span>Linux Administration</span>
                            </div>
                            <div class="tech-item">
                                <i class="fas fa-windows"></i>
                                <span>Windows Server</span>
                            </div>
                            <div class="tech-item">
                                <i class="fas fa-hdd"></i>
                                <span>Virtualization</span>
                            </div>
                            <div class="tech-item">
                                <i class="fas fa-database"></i>
                                <span>Database Management</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monitoring & Cloud -->
                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="tech-category-card">
                        <div class="tech-category-header">
                            <div class="tech-image-container mb-3">
                                <img src="{{ asset('images/cloud-monitoring.svg') }}" alt="Monitoring & Cloud" style="width: 100px; height: 75px; object-fit: contain;">
                            </div>
                            <h4 class="text-white mb-3">Monitoring & Cloud</h4>
                        </div>
                        <div class="tech-items">
                            <div class="tech-item">
                                <i class="fas fa-eye"></i>
                                <span>Zabbix Monitoring</span>
                            </div>
                            <div class="tech-item">
                                <i class="fas fa-chart-bar"></i>
                                <span>PRTG Network Monitor</span>
                            </div>
                            <div class="tech-item">
                                <i class="fab fa-aws"></i>
                                <span>AWS Cloud</span>
                            </div>
                            <div class="tech-item">
                                <i class="fab fa-docker"></i>
                                <span>Docker Containers</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
   {{-- <section class="stats-section">
        <div class="container">
            <div class="row g-4" data-aos="fade-up" data-aos-duration="1000">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number" data-count="{{ $experiences->count() }}">0</div>
                        <div class="stats-label">Years Experience</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number" data-count="{{ $skills->count() }}">0</div>
                        <div class="stats-label">Technical Skills</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number" data-count="{{ $certifications->count() }}">0</div>
                        <div class="stats-label">Certifications</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number" data-count="50">0</div>
                        <div class="stats-label">Projects Completed</div>
                    </div>
                </div>
            </div>
        </div>
    </section> --}}

    <!-- About Section -->
    @if($profile)
    <section id="about" class="py-5" style="background: var(--light-color);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right" data-aos-duration="1000">
                    <div class="mb-4">
                        <span class="text-primary fw-bold text-uppercase tracking-wider">About Me</span>
                        <h2 class="display-5 fw-bold mt-2 mb-4">Passionate DevOps Engineer</h2>
                    </div>

                    <p class="lead mb-4">{{ $profile->description }}</p>

                    <div class="row g-4 mb-4">
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                                    <i class="fas fa-map-marker-alt text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">Location</h6>
                                    <small class="text-muted">{{ $profile->address }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-success bg-opacity-10 rounded-circle p-3 me-3">
                                    <i class="fas fa-briefcase text-success"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">Freelance</h6>
                                    <small class="text-muted">{{ $profile->freelance_available ? 'Available' : 'Not Available' }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-info bg-opacity-10 rounded-circle p-3 me-3">
                                    <i class="fas fa-language text-info"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">Languages</h6>
                                    <small class="text-muted">{{ is_array($profile->languages) ? implode(', ', $profile->languages) : $profile->languages }}</small>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="d-flex gap-3">
                        <a href="mailto:{{ $profile->email }}" class="btn btn-primary-modern">
                            <i class="fas fa-envelope me-2"></i>Get In Touch
                        </a>
                        <a href="{{ asset('cv/CV_Tsiky.pdf') }}" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-download me-2"></i>Download CV
                        </a>
                    </div>
                </div>

                <div class="col-lg-6" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                    <div class="position-relative">
                        <!-- Tech Stack Icons -->
                        <div class="row g-3">
                            <div class="col-4">
                                <div class="bg-white rounded-4 p-4 text-center shadow-sm">
                                    <i class="fab fa-linux fa-2x text-primary mb-2"></i>
                                    <h6 class="mb-0">Linux</h6>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="bg-white rounded-4 p-4 text-center shadow-sm">
                                    <i class="fas fa-server fa-2x text-success mb-2"></i>
                                    <h6 class="mb-0">Servers</h6>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="bg-white rounded-4 p-4 text-center shadow-sm">
                                    <i class="fas fa-cloud fa-2x text-info mb-2"></i>
                                    <h6 class="mb-0">Cloud</h6>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="bg-white rounded-4 p-4 text-center shadow-sm">
                                    <i class="fas fa-network-wired fa-2x text-warning mb-2"></i>
                                    <h6 class="mb-0">Network</h6>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="bg-white rounded-4 p-4 text-center shadow-sm">
                                    <i class="fas fa-shield-alt fa-2x text-danger mb-2"></i>
                                    <h6 class="mb-0">Security</h6>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="bg-white rounded-4 p-4 text-center shadow-sm">
                                    <i class="fas fa-chart-line fa-2x text-purple mb-2"></i>
                                    <h6 class="mb-0">Monitoring</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Experience Section -->
    @if($experiences->count() > 0)
    <section id="experience" class="py-5">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up" data-aos-duration="1000">
                <span class="text-primary fw-bold text-uppercase tracking-wider">Experience</span>
                <h2 class="display-5 fw-bold mt-2">Professional Journey</h2>
                <p class="lead text-muted">My career path and professional achievements</p>
            </div>

            <div class="row">
                @foreach($experiences as $index => $experience)
                <div class="col-lg-12 mb-4" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="{{ $index * 200 }}">
                    <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                        <div class="card-body p-4">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <div class="d-flex align-items-start mb-3">
                                        <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3 flex-shrink-0">
                                            <i class="fas fa-briefcase text-primary"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h4 class="mb-1">{{ $experience->position }}</h4>
                                            <h5 class="text-primary mb-1">{{ $experience->company }}</h5>
                                            @if($experience->location)
                                                <p class="text-muted mb-2">
                                                    <i class="fas fa-map-marker-alt me-1"></i>{{ $experience->location }}
                                                </p>
                                            @endif
                                            <p class="mb-3">{{ $experience->description }}</p>
                                        </div>
                                    </div>

                                    @if($experience->responsibilities && count($experience->responsibilities) > 0)
                                        <div class="mb-3">
                                            <h6 class="fw-bold mb-2">Key Responsibilities:</h6>
                                            <div class="row">
                                                @foreach($experience->responsibilities as $responsibility)
                                                    <div class="col-md-6 mb-2">
                                                        <div class="d-flex align-items-start">
                                                            <i class="fas fa-check-circle text-success me-2 mt-1 flex-shrink-0"></i>
                                                            <span class="small">{{ $responsibility }}</span>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <div class="col-lg-4 text-lg-end">
                                    <div class="mb-3">
                                        <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 rounded-pill">
                                            {{ $experience->date_range }}
                                        </span>
                                    </div>
                                    @if($experience->current)
                                        <div class="mb-2">
                                            <span class="badge bg-success bg-opacity-10 text-success px-3 py-2 rounded-pill">
                                                <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>Current Position
                                            </span>
                                        </div>
                                    @endif
                                    <div>
                                        <span class="badge bg-secondary bg-opacity-10 text-secondary px-3 py-2 rounded-pill">
                                            {{ ucfirst($experience->employment_type) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Skills Section -->
    @if($skills->count() > 0)
    <section id="skills" class="py-5" style="background: var(--light-color);">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up" data-aos-duration="1000">
                <span class="text-primary fw-bold text-uppercase tracking-wider">Skills</span>
                <h2 class="display-5 fw-bold mt-2">Technical Expertise</h2>
                <p class="lead text-muted">Technologies and tools I work with</p>
            </div>

            <div class="row g-4">
                @foreach($skills as $index => $skill)
                <div class="col-lg-6" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="{{ $index * 100 }}">
                    <div class="bg-white rounded-4 p-4 shadow-sm h-100">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                                <i class="{{ $skill->icon ?? 'fas fa-cog' }} text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="mb-1">{{ $skill->name }}</h5>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted small">{{ ucfirst($skill->category) }}</span>
                                    <span class="fw-bold text-primary">{{ $skill->level }}%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Modern Progress Bar -->
                        <div class="progress" style="height: 8px; background-color: #e9ecef;">
                            <div class="progress-bar"
                                 style="background: var(--gradient-primary); width: 0%; transition: width 2s ease-in-out;"
                                 data-width="{{ $skill->level }}%">
                            </div>
                        </div>

                        @if($skill->description)
                            <p class="text-muted small mt-3 mb-0">{{ $skill->description }}</p>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Skills Categories -->
            <div class="row mt-5">
                <div class="col-12" data-aos="fade-up" data-aos-duration="1000">
                    <div class="bg-white rounded-4 p-4 shadow-sm">
                        <h4 class="mb-4 text-center">Skill Categories</h4>
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <div class="p-3">
                                    <i class="fas fa-server fa-2x text-primary mb-2"></i>
                                    <h6>System Administration</h6>
                                    <small class="text-muted">Linux, Windows Server, Virtualization</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="p-3">
                                    <i class="fas fa-network-wired fa-2x text-success mb-2"></i>
                                    <h6>Network Management</h6>
                                    <small class="text-muted">Cisco, Mikrotik, Firewall Configuration</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="p-3">
                                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                    <h6>Monitoring & Analytics</h6>
                                    <small class="text-muted">Zabbix, PRTG, LibreNMS</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="p-3">
                                    <i class="fas fa-code fa-2x text-warning mb-2"></i>
                                    <h6>Development</h6>
                                    <small class="text-muted">Laravel, JavaScript, Web Technologies</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Education Section -->
    @if($educations->count() > 0)
    <section id="education" class="py-5">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up" data-aos-duration="1000">
                <span class="text-primary fw-bold text-uppercase tracking-wider">Education</span>
                <h2 class="display-5 fw-bold mt-2">Academic Background</h2>
                <p class="lead text-muted">My educational journey and qualifications</p>
            </div>

            <div class="row g-4">
                @foreach($educations as $index => $education)
                <div class="col-lg-6" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="{{ $index * 200 }}">
                    <div class="bg-white rounded-4 p-4 shadow-lg h-100 border-0">
                        <div class="d-flex align-items-start mb-3">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3 flex-shrink-0">
                                <i class="fas fa-graduation-cap text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h4 class="mb-1">{{ $education->degree }}</h4>
                                @if($education->field_of_study)
                                    <h6 class="text-primary mb-2">{{ $education->field_of_study }}</h6>
                                @endif
                                <h6 class="fw-bold mb-1">{{ $education->institution }}</h6>
                                @if($education->location)
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ $education->location }}
                                    </p>
                                @endif
                                <div class="mb-3">
                                    <span class="badge bg-info bg-opacity-10 text-info px-3 py-2 rounded-pill">
                                        {{ $education->date_range }}
                                    </span>
                                    @if($education->grade)
                                        <span class="badge bg-success bg-opacity-10 text-success px-3 py-2 rounded-pill ms-2">
                                            {{ $education->grade }}
                                        </span>
                                    @endif
                                </div>
                                @if($education->description)
                                    <p class="text-muted mb-0">{{ $education->description }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Certifications Section -->
    @if($certifications->count() > 0)
    <section class="py-5" style="background: var(--light-color);">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up" data-aos-duration="1000">
                <span class="text-primary fw-bold text-uppercase tracking-wider">Certifications</span>
                <h2 class="display-5 fw-bold mt-2">Professional Certifications</h2>
                <p class="lead text-muted">Industry-recognized credentials and achievements</p>
            </div>

            <div class="row g-4">
                @foreach($certifications as $index => $certification)
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="{{ $index * 100 }}">
                    <div class="bg-white rounded-4 p-4 shadow-sm text-center h-100 border-0">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-4 d-inline-flex align-items-center justify-content-center mb-3">
                            <i class="{{ $certification->icon ?? 'fas fa-certificate' }} fa-2x text-primary"></i>
                        </div>
                        <h5 class="mb-2">{{ $certification->name }}</h5>
                        <h6 class="text-primary mb-3">{{ $certification->issuing_organization }}</h6>

                        @if($certification->description)
                            <p class="text-muted small mb-3">{{ $certification->description }}</p>
                        @endif

                        <div class="mb-3">
                            <span class="badge bg-success bg-opacity-10 text-success px-3 py-2 rounded-pill">
                                {{ $certification->date_display }}
                            </span>
                        </div>

                        <div class="mb-3">
                            @if($certification->credential_url)
                                <a href="{{ $certification->credential_url }}" target="_blank" class="btn btn-primary btn-sm px-3 py-2">
                                    <i class="fas fa-external-link-alt me-2"></i>{{ __('portfolio.view_credential') }}
                                </a>
                            @else
                                {{-- URLs de démonstration pour les certifications --}}
                                @php
                                    $demoUrls = [
                                        'https://www.credly.com/badges/demo-aws-certification',
                                        'https://www.coursera.org/account/accomplishments/certificate/demo-cert',
                                        'https://www.udemy.com/certificate/demo-certificate/',
                                        'https://learn.microsoft.com/en-us/users/demo/credentials/',
                                        'https://www.linkedin.com/learning/certificates/demo-cert'
                                    ];
                                    $randomUrl = $demoUrls[array_rand($demoUrls)];
                                @endphp
                                <a href="{{ $randomUrl }}" target="_blank" class="btn btn-primary btn-sm px-3 py-2">
                                    <i class="fas fa-external-link-alt me-2"></i>{{ __('portfolio.view_credential') }}
                                </a>
                            @endif
                        </div>

                        @if($certification->credential_id)
                            <div class="border-top pt-3">
                                <small class="text-muted">
                                    <i class="fas fa-id-card me-1"></i>
                                    ID: {{ $certification->credential_id }}
                                </small>
                            </div>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Projects Section -->
    <section id="projects" class="py-5">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">{{ __('portfolio.projects_title') }}</h2>
                <p class="section-subtitle">{{ __('portfolio.projects_subtitle') }}</p>
            </div>

            @if($projects->count() > 0)
                <!-- Filter Tabs -->
                <div class="text-center mb-4" data-aos="fade-up" data-aos-delay="100">
                    <ul class="nav nav-pills justify-content-center" id="projectTabs">
                        <li class="nav-item">
                            <button class="nav-link active" data-filter="all">
                                <i class="fas fa-th me-2"></i>Tous
                            </button>
                        </li>
                        @if($projects->where('type', 'project')->count() > 0)
                            <li class="nav-item">
                                <button class="nav-link" data-filter="project">
                                    <i class="fas fa-folder me-2"></i>{{ __('portfolio.project_type_project') }}s
                                </button>
                            </li>
                        @endif
                        @if($projects->where('type', 'lab')->count() > 0)
                            <li class="nav-item">
                                <button class="nav-link" data-filter="lab">
                                    <i class="fas fa-flask me-2"></i>{{ __('portfolio.project_type_lab') }}s
                                </button>
                            </li>
                        @endif
                        @if($projects->where('type', 'demo')->count() > 0)
                            <li class="nav-item">
                                <button class="nav-link" data-filter="demo">
                                    <i class="fas fa-desktop me-2"></i>{{ __('portfolio.project_type_demo') }}s
                                </button>
                            </li>
                        @endif
                    </ul>
                </div>

                <!-- Projects Grid -->
                <div class="row" id="projectsGrid">
                    @foreach($projects as $project)
                        <div class="col-lg-4 col-md-6 mb-4 project-item"
                             data-type="{{ $project->type }}"
                             data-aos="fade-up"
                             data-aos-delay="{{ $loop->index * 100 }}">
                            <div class="card project-card h-100 shadow-sm">
                                <div class="position-relative overflow-hidden">
                                    <img src="{{ $project->image_url }}"
                                         class="card-img-top project-image"
                                         alt="{{ $project->title }}"
                                         style="height: 200px; object-fit: cover; transition: transform 0.3s ease;">

                                    <!-- Overlay -->
                                    <div class="project-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center">
                                            @if($project->project_url)
                                                <a href="{{ $project->project_url }}" target="_blank"
                                                   class="btn btn-primary btn-sm me-2 mb-2">
                                                    <i class="fas fa-external-link-alt me-1"></i>{{ __('portfolio.view_project') }}
                                                </a>
                                            @endif
                                            @if($project->github_url)
                                                <a href="{{ $project->github_url }}" target="_blank"
                                                   class="btn btn-dark btn-sm me-2 mb-2">
                                                    <i class="fab fa-github me-1"></i>{{ __('portfolio.view_github') }}
                                                </a>
                                            @endif
                                            @if($project->demo_url)
                                                <a href="{{ $project->demo_url }}" target="_blank"
                                                   class="btn btn-success btn-sm mb-2">
                                                    <i class="fas fa-play me-1"></i>{{ __('portfolio.view_demo') }}
                                                </a>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Type Badge -->
                                    <span class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-{{ $project->type_badge['class'] }}">
                                            <i class="fas fa-{{ $project->type === 'project' ? 'folder' : ($project->type === 'lab' ? 'flask' : 'desktop') }} me-1"></i>
                                            {{ __('portfolio.project_type_' . $project->type) }}
                                        </span>
                                    </span>

                                    <!-- Featured Badge -->
                                    @if($project->is_featured)
                                        <span class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-star me-1"></i>Featured
                                            </span>
                                        </span>
                                    @endif
                                </div>

                                <div class="card-body">
                                    <h5 class="card-title">{{ $project->title }}</h5>
                                    @if($project->category)
                                        <span class="badge bg-secondary mb-2">{{ $project->category }}</span>
                                    @endif
                                    <p class="card-text text-muted">{{ Str::limit($project->description, 120) }}</p>

                                    @if($project->technologies && count($project->technologies) > 0)
                                        <div class="mb-3">
                                            @foreach(array_slice($project->technologies, 0, 4) as $tech)
                                                <span class="badge bg-light text-dark me-1 mb-1">{{ $tech }}</span>
                                            @endforeach
                                            @if(count($project->technologies) > 4)
                                                <span class="badge bg-light text-dark">+{{ count($project->technologies) - 4 }}</span>
                                            @endif
                                        </div>
                                    @endif

                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>{{ $project->duration }}
                                        </small>
                                        <span class="badge bg-{{ $project->status_badge['class'] }}">
                                            {{ __('portfolio.project_status_' . $project->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-5" data-aos="fade-up">
                    <div class="mb-4">
                        <i class="fas fa-folder-open fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">Projets à venir</h4>
                    <p class="text-muted">De nouveaux projets seront bientôt disponibles.</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Contact Section -->
    @if($profile)
    <section id="contact" class="py-5" style="background: var(--gradient-dark);">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up" data-aos-duration="1000">
                <span class="text-white fw-bold text-uppercase tracking-wider opacity-75">Contact</span>
                <h2 class="display-5 fw-bold mt-2 text-white">Let's Work Together</h2>
                <p class="lead text-white opacity-75">Ready to start your next project? Let's discuss how I can help.</p>
            </div>

            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right" data-aos-duration="1000">
                    <div class="mb-5">
                        <h3 class="text-white mb-4">Get In Touch</h3>
                        <div class="row g-4">
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center text-white">
                                    <div class="bg-white bg-opacity-10 rounded-circle p-3 me-3">
                                        <i class="fas fa-envelope text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 text-white">Email</h6>
                                        <a href="mailto:{{ $profile->email }}" class="text-white opacity-75 text-decoration-none">
                                            {{ $profile->email }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center text-white">
                                    <div class="bg-white bg-opacity-10 rounded-circle p-3 me-3">
                                        <i class="fas fa-phone text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 text-white">Phone</h6>
                                        <span class="text-white opacity-75">{{ $profile->phone }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center text-white">
                                    <div class="bg-white bg-opacity-10 rounded-circle p-3 me-3">
                                        <i class="fas fa-map-marker-alt text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 text-white">Location</h6>
                                        <span class="text-white opacity-75">{{ $profile->address }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center text-white">
                                    <div class="bg-white bg-opacity-10 rounded-circle p-3 me-3">
                                        <i class="fas fa-briefcase text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 text-white">Status</h6>
                                        <span class="text-white opacity-75">
                                            {{ $profile->freelance_available ? 'Available for Freelance' : 'Currently Unavailable' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3 mb-4">
                        <a href="mailto:{{ $profile->email }}" class="btn btn-primary-modern">
                            <i class="fas fa-envelope me-2"></i>Send Email
                        </a>
                        <a href="{{ asset('cv/CV_Tsiky.pdf') }}" target="_blank" class="btn btn-outline-modern">
                            <i class="fas fa-download me-2"></i>Download CV
                        </a>
                    </div>

                    <!-- Social Links -->
                    <div class="d-flex gap-3">
                        @if($profile->linkedin)
                            <a href="{{ $profile->linkedin }}" target="_blank" class="text-white fs-4 opacity-75 hover-opacity-100" data-bs-toggle="tooltip" title="LinkedIn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        @endif
                        @if($profile->github)
                            <a href="{{ $profile->github }}" target="_blank" class="text-white fs-4 opacity-75 hover-opacity-100" data-bs-toggle="tooltip" title="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                        @endif
                        @if($profile->website)
                            <a href="{{ $profile->website }}" target="_blank" class="text-white fs-4 opacity-75 hover-opacity-100" data-bs-toggle="tooltip" title="Website">
                                <i class="fas fa-globe"></i>
                            </a>
                        @endif
                    </div>
                </div>

                <div class="col-lg-6" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                    <div class="bg-white bg-opacity-10 rounded-4 p-4 backdrop-blur">
                        <h4 class="text-white mb-4">
                            <i class="fas fa-paper-plane me-2"></i>Envoyez-moi un message
                        </h4>

                        <!-- Contact Form -->
                        <form id="contactForm" class="contact-form" action="{{ route('contact.send') }}" method="POST">
                            @csrf
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control bg-white bg-opacity-20 border-0 text-white"
                                               id="name" name="name"  required>
                                        <label for="name" class="text-white opacity-75">
                                            <i class="fas fa-user me-2"></i>Votre nom *
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" class="form-control bg-white bg-opacity-20 border-0 text-white"
                                               id="email" name="email"  required>
                                        <label for="email" class="text-white opacity-75">
                                            <i class="fas fa-envelope me-2"></i>Votre email *
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-floating">
                                        <input type="text" class="form-control bg-white bg-opacity-20 border-0 text-white"
                                               id="subject" name="subject"  required>
                                        <label for="subject" class="text-white opacity-75">
                                            <i class="fas fa-tag me-2"></i>Sujet *
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-floating">
                                        <textarea class="form-control bg-white bg-opacity-20 border-0 text-white"
                                                  id="message" name="message" 
                                                  style="height: 120px;" required></textarea>
                                        <label for="message" class="text-white opacity-75">
                                            <i class="fas fa-comment me-2"></i>Votre message *
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary-modern w-100" id="submitBtn">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        <span class="btn-text">Envoyer le message</span>
                                        <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Success/Error Messages -->
                        <div id="contactAlert" class="alert d-none mt-3" role="alert"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Presentation Video Modal -->
    @if($presentationVideo)
    <div class="modal fade" id="presentationModal" tabindex="-1" aria-labelledby="presentationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content bg-dark border-0">
                <div class="modal-header border-0">
                    <h5 class="modal-title text-white" id="presentationModalLabel">
                        <i class="fas fa-video me-2"></i>{{ $presentationVideo->title }}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    @if($presentationVideo->video_type === 'upload')
                        <video controls class="w-100" style="max-height: 500px;" id="presentationVideoElement">
                            <source src="{{ $presentationVideo->embed_url }}" type="video/mp4">
                            Votre navigateur ne supporte pas la lecture vidéo.
                        </video>
                    @else
                        <div class="ratio ratio-16x9">
                            <iframe id="presentationVideo"
                                    src=""
                                    title="{{ $presentationVideo->title }}"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen>
                            </iframe>
                        </div>
                    @endif
                </div>
                @if($presentationVideo->description)
                    <div class="modal-footer border-0 bg-dark">
                        <div class="text-center w-100">
                            <p class="text-white opacity-75 mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                {{ $presentationVideo->description }}
                            </p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Footer -->
    <footer class="py-4" style="background: var(--dark-color);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-white opacity-75 mb-0">
                        &copy; {{ date('Y') }} {{ $profile->name ?? 'Portfolio' }}. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-white opacity-50 mb-0 small">
                        Built with <i class="fas fa-heart text-danger"></i> By Tsiky using Laravel 
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });

        // Language dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const languageDropdown = document.getElementById('languageDropdown');
            if (languageDropdown) {
                languageDropdown.addEventListener('click', function(e) {
                    e.preventDefault();
                    // Bootstrap will handle the dropdown toggle
                });
            }
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-modern');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('[data-count]');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-count'));
                const duration = 2000;
                const step = target / (duration / 16);
                let current = 0;

                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 16);
            });
        }

        // Animate skill progress bars
        function animateSkillBars() {
            const progressBars = document.querySelectorAll('.progress-bar[data-width]');
            progressBars.forEach(bar => {
                const width = bar.getAttribute('data-width');
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        }

        // Trigger animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(animateCounters, 1000);
            setTimeout(animateSkillBars, 1500);
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.stats-card, .progress-bar').forEach(el => {
            observer.observe(el);
        });

        // Contact Form Handling
        document.getElementById('contactForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const spinner = submitBtn.querySelector('.spinner-border');
            const alertDiv = document.getElementById('contactAlert');

            // Show loading state
            submitBtn.disabled = true;
            btnText.textContent = 'Envoi en cours...';
            spinner.classList.remove('d-none');
            alertDiv.classList.add('d-none');

            try {
                const formData = new FormData(this);
                const response = await fetch('{{ route("contact.send") }}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '{{ csrf_token() }}'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // Success
                    alertDiv.className = 'alert alert-success mt-3';
                    alertDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + result.message;
                    alertDiv.classList.remove('d-none');
                    this.reset();
                } else {
                    // Error
                    alertDiv.className = 'alert alert-danger mt-3';
                    alertDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>' + result.message;
                    alertDiv.classList.remove('d-none');
                }
            } catch (error) {
                // Network error
                alertDiv.className = 'alert alert-danger mt-3';
                alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Erreur de connexion. Veuillez réessayer.';
                alertDiv.classList.remove('d-none');
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                btnText.textContent = 'Envoyer le message';
                spinner.classList.add('d-none');
            }
        });

        // Presentation Video Modal
        @if($presentationVideo)
        const presentationModal = document.getElementById('presentationModal');

        @if($presentationVideo->video_type === 'upload')
            const presentationVideoElement = document.getElementById('presentationVideoElement');

            presentationModal.addEventListener('hide.bs.modal', function () {
                if (presentationVideoElement) {
                    presentationVideoElement.pause();
                    presentationVideoElement.currentTime = 0;
                }
            });
        @else
            const presentationVideo = document.getElementById('presentationVideo');
            const videoUrl = '{{ $presentationVideo->embed_url }}';

            presentationModal.addEventListener('show.bs.modal', function () {
                if (presentationVideo) {
                    presentationVideo.src = videoUrl;
                }
            });

            presentationModal.addEventListener('hide.bs.modal', function () {
                if (presentationVideo) {
                    presentationVideo.src = '';
                }
            });
        @endif
        @endif

        // Projects Filter
        document.querySelectorAll('#projectTabs .nav-link').forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('#projectTabs .nav-link').forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                const filter = this.dataset.filter;
                const projectItems = document.querySelectorAll('.project-item');

                projectItems.forEach(item => {
                    if (filter === 'all' || item.dataset.type === filter) {
                        item.classList.remove('hidden');
                    } else {
                        item.classList.add('hidden');
                    }
                });
            });
        });
    </script>
</body>
</html>
