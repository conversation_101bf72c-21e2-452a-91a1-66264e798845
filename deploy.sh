#!/bin/bash

# Script de déploiement pour corriger la page blanche
# Usage: bash deploy.sh

echo "🚀 Déploiement du portfolio - Correction page blanche"
echo "=================================================="

# 1. Sauvegarde
echo "📦 Création d'une sauvegarde..."
if [ -d "/var/www/html" ]; then
    sudo cp -r /var/www/html /var/www/html_backup_$(date +%Y%m%d_%H%M%S)
    echo "✅ Sauvegarde créée"
fi

# 2. Nettoyage du cache Laravel
echo "🧹 Nettoyage du cache..."
cd /var/www/tsikyportfolio
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear
echo "✅ Cache nettoyé"

# 3. Vérification des permissions
echo "🔐 Vérification des permissions..."
sudo chmod -R 755 storage/
sudo chmod -R 755 bootstrap/cache/
sudo chown -R www-data:www-data storage/
sudo chown -R www-data:www-data bootstrap/cache/
echo "✅ Permissions corrigées"

# 4. Test de la base de données
echo "🗄️ Test de la base de données..."
php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'DB OK'; } catch(Exception \$e) { echo 'DB Error: ' . \$e->getMessage(); }"

# 5. Test des routes
echo "🌐 Test des routes..."
echo "Route test:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/test
echo ""
echo "Route principale:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:8083
echo ""
echo "Route simple:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/simple
echo ""

# 6. Redémarrage d'Apache (si nécessaire)
echo "🔄 Redémarrage d'Apache..."
sudo systemctl reload apache2
echo "✅ Apache rechargé"

# 7. Test final
echo "🎯 Test final..."
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8083)
if [ "$RESPONSE" = "200" ]; then
    echo "✅ Site accessible - Code: $RESPONSE"
else
    echo "❌ Problème détecté - Code: $RESPONSE"
fi

echo ""
echo "🎉 Déploiement terminé!"
echo "📋 URLs de test:"
echo "   - Principal: http://localhost:8083"
echo "   - Simple: http://localhost:8083/simple"
echo "   - Debug: http://localhost:8083/debug"
echo "   - Test: http://localhost:8083/test"
