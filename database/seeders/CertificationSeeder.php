<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CertificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $certifications = [
            ['name' => 'Spoken English Course', 'issuing_organization' => 'ITTI', 'issue_date' => '2018-12-01', 'expiration_date' => null, 'credential_id' => null, 'credential_url' => null, 'description' => 'Advanced level English communication course', 'icon' => 'fas fa-certificate', 'order' => 1],
            ['name' => 'NDG Linux Unhatched', 'issuing_organization' => 'Cisco Network Academy', 'issue_date' => '2019-06-01', 'expiration_date' => null, 'credential_id' => '1038881810', 'credential_url' => null, 'description' => 'Introduction to Linux operating system', 'icon' => 'fab fa-linux', 'order' => 2],
            ['name' => 'Windows Server 2012 R2', 'issuing_organization' => 'Alison', 'issue_date' => '2019-03-01', 'expiration_date' => null, 'credential_id' => '1754-17864638', 'credential_url' => null, 'description' => 'Server Administration Services', 'icon' => 'fas fa-server', 'order' => 3],
            ['name' => 'Linux Network Administrator', 'issuing_organization' => 'Alison', 'issue_date' => '2019-04-01', 'expiration_date' => null, 'credential_id' => '3517-17864638', 'credential_url' => null, 'description' => 'Linux network administration and management', 'icon' => 'fas fa-network-wired', 'order' => 4],
            ['name' => 'Office 365 Administration', 'issuing_organization' => 'Alison', 'issue_date' => '2020-02-01', 'expiration_date' => null, 'credential_id' => '1024-19148330', 'credential_url' => null, 'description' => 'Administration Basics for Office 365', 'icon' => 'fab fa-microsoft', 'order' => 5],
            ['name' => 'JavaScript & jQuery', 'issuing_organization' => 'SoloLearn & Eduonix', 'issue_date' => '2020-08-01', 'expiration_date' => null, 'credential_id' => null, 'credential_url' => null, 'description' => 'Multiple certifications in JavaScript and jQuery development', 'icon' => 'fab fa-js-square', 'order' => 6]
        ];

        foreach ($certifications as $certification) {
            \App\Models\Certification::create($certification);
        }
    }
}
