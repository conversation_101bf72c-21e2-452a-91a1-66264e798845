<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SkillSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $skills = [
            ['name' => 'Network Administration (Mikrotik, Cisco)', 'category' => 'technical', 'level' => 90, 'description' => 'Expert in network configuration, routing, switching, and security', 'icon' => 'fas fa-network-wired', 'order' => 1],
            ['name' => 'Windows Server (2012) / Linux (Ubuntu, Debian, Rocky)', 'category' => 'technical', 'level' => 85, 'description' => 'Advanced server administration and management', 'icon' => 'fas fa-server', 'order' => 2],
            ['name' => 'Firewall (PFsense, Fortinet, Stormshield, Sophos)', 'category' => 'technical', 'level' => 88, 'description' => 'Security implementation and firewall management', 'icon' => 'fas fa-shield-alt', 'order' => 3],
            ['name' => 'Monitoring (Zabbix, LibreNMS, PRTG)', 'category' => 'technical', 'level' => 82, 'description' => 'Infrastructure monitoring and alerting systems', 'icon' => 'fas fa-chart-line', 'order' => 4],
            ['name' => 'Virtualization (VMware ESXi, Proxmox)', 'category' => 'technical', 'level' => 87, 'description' => 'Virtual infrastructure design and management', 'icon' => 'fas fa-cube', 'order' => 5],
            ['name' => 'Server Deployment (AD, WDS, Web, VPN)', 'category' => 'technical', 'level' => 85, 'description' => 'Server deployment and configuration', 'icon' => 'fas fa-cogs', 'order' => 6],
            ['name' => 'Helpdesk & Ticketing (GLPI)', 'category' => 'technical', 'level' => 90, 'description' => 'IT support and ticket management systems', 'icon' => 'fas fa-headset', 'order' => 7],
            ['name' => 'Web Development (Laravel, JavaScript, jQuery)', 'category' => 'technical', 'level' => 78, 'description' => 'Full-stack web development', 'icon' => 'fas fa-code', 'order' => 8]
        ];

        foreach ($skills as $skill) {
            \App\Models\Skill::create($skill);
        }
    }
}
