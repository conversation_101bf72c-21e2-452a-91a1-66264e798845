<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EducationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $educations = [
            [
                'degree' => 'License in Network & System Administration',
                'field_of_study' => 'Computer Science and Telecommunications',
                'institution' => 'University E-media',
                'location' => 'Ampasanimalo, Antananarivo',
                'start_date' => '2016-01-01',
                'end_date' => '2018-12-31',
                'description' => 'Academic Course in Computer Science and Telecommunications with focus on network infrastructure and system administration',
                'grade' => null,
                'order' => 1
            ],
            [
                'degree' => 'Baccalauréat',
                'field_of_study' => 'General Studies',
                'institution' => 'Lycée Privé Adventiste Rajoelisono Soamanadrariny',
                'location' => 'Antananarivo, Madagascar',
                'start_date' => '2014-01-01',
                'end_date' => '2015-12-31',
                'description' => 'High School Diploma with honors',
                'grade' => 'With honors',
                'order' => 2
            ]
        ];

        foreach ($educations as $education) {
            \App\Models\Education::create($education);
        }
    }
}
