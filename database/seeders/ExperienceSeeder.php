<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExperienceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $experiences = [
            [
                'position' => 'AdminSyS - IT Support',
                'company' => 'AVEOLYS',
                'location' => 'Antananarivo, Madagascar',
                'start_date' => '2024-09-01',
                'end_date' => null,
                'current' => true,
                'description' => 'IT Park Management and Infrastructure Support',
                'responsibilities' => [
                    'IT Park Management (Kaseya VSA)',
                    'User Support & Assistance',
                    'Infrastructure Monitoring (PRTG)',
                    'Backup Management (Veeam, Nakivo, Zerto, SyncBack)',
                    'User Workstation & Server Maintenance',
                    'Server Administration (VCenter - PROXMOX)',
                    'Network Administration (Switch - Firewall Sophos & Stormshield)'
                ],
                'employment_type' => 'full-time',
                'order' => 1
            ],
            [
                'position' => 'Network & System Administrator',
                'company' => 'MASTER-IMMO',
                'location' => 'Antananarivo, Madagascar',
                'start_date' => '2022-01-01',
                'end_date' => '2024-08-31',
                'current' => false,
                'description' => 'Freelance Network and System Administration',
                'responsibilities' => [
                    'Network configuration, maintenance and server deployment',
                    'Proxmox Virtualization',
                    'Zabbix Monitoring',
                    'SMTP Administration with Zimbra'
                ],
                'employment_type' => 'freelance',
                'order' => 2
            ],
            [
                'position' => 'IT Support Specialist',
                'company' => 'BMOI - BCP Group',
                'location' => 'Antananarivo, Madagascar',
                'start_date' => '2022-09-01',
                'end_date' => '2022-12-31',
                'current' => false,
                'description' => 'IT Support and Helpdesk Services',
                'responsibilities' => [
                    'IT Support & Helpdesk',
                    'Maintenance Ticket Management (GLPI)',
                    'IP Workstation Network Deployment',
                    'Network Troubleshooting',
                    'Bandwidth Monitoring',
                    'Active Directory Administration'
                ],
                'employment_type' => 'freelance',
                'order' => 3
            ]
        ];

        foreach ($experiences as $experience) {
            \App\Models\Experience::create($experience);
        }
    }
}
