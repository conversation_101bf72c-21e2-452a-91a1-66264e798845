<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Video;

class VideoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer une vidéo de présentation de test
        Video::create([
            'title' => 'Ma Présentation Professionnelle',
            'description' => 'Découvrez mon parcours, mes compétences et ma passion pour la technologie DevOps.',
            'video_type' => 'youtube',
            'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'is_active' => true,
            'is_presentation' => true,
            'duration' => 180,
            'order' => 0
        ]);

        // Créer quelques autres vidéos d'exemple
        Video::create([
            'title' => 'Démonstration Docker & Kubernetes',
            'description' => 'Une démonstration de mes compétences en containerisation.',
            'video_type' => 'youtube',
            'video_url' => 'https://www.youtube.com/watch?v=3c-iBn73dDE',
            'is_active' => true,
            'is_presentation' => false,
            'duration' => 300,
            'order' => 1
        ]);

        Video::create([
            'title' => 'Projet CI/CD avec Jenkins',
            'description' => 'Mise en place d\'un pipeline CI/CD complet.',
            'video_type' => 'vimeo',
            'video_url' => 'https://vimeo.com/123456789',
            'is_active' => true,
            'is_presentation' => false,
            'duration' => 420,
            'order' => 2
        ]);
    }
}
