<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Project;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Projets DevOps
        Project::create([
            'title' => 'Infrastructure as Code avec Terraform',
            'description' => 'Déploiement automatisé d\'une infrastructure cloud complète sur AWS avec Terraform, incluant VPC, EC2, RDS, et Load Balancer. Configuration de monitoring avec CloudWatch et alertes automatiques.',
            'type' => 'project',
            'category' => 'DevOps',
            'project_url' => 'https://github.com/tsknitokiana/terraform-aws-infrastructure',
            'github_url' => 'https://github.com/tsknitokiana/terraform-aws-infrastructure',
            'technologies' => ['Terraform', 'AWS', 'CloudWatch', 'VPC', 'EC2', 'RDS'],
            'status' => 'completed',
            'start_date' => '2024-01-15',
            'end_date' => '2024-03-20',
            'is_featured' => true,
            'is_active' => true,
            'order' => 1
        ]);

        Project::create([
            'title' => 'Pipeline CI/CD avec Jenkins et Docker',
            'description' => 'Mise en place d\'un pipeline CI/CD complet avec Jenkins, Docker, et Kubernetes. Automatisation des tests, build, et déploiement avec rollback automatique en cas d\'échec.',
            'type' => 'project',
            'category' => 'DevOps',
            'project_url' => 'https://jenkins.tsikyportfolio.com',
            'github_url' => 'https://github.com/tsknitokiana/jenkins-docker-pipeline',
            'demo_url' => 'https://demo.tsikyportfolio.com',
            'technologies' => ['Jenkins', 'Docker', 'Kubernetes', 'Git', 'SonarQube'],
            'status' => 'completed',
            'start_date' => '2024-02-01',
            'end_date' => '2024-04-15',
            'is_featured' => true,
            'is_active' => true,
            'order' => 2
        ]);

        // Labs d'expérimentation
        Project::create([
            'title' => 'Lab Kubernetes Multi-Cluster',
            'description' => 'Laboratoire d\'expérimentation avec Kubernetes multi-cluster, service mesh avec Istio, et monitoring avec Prometheus/Grafana. Test de haute disponibilité et disaster recovery.',
            'type' => 'lab',
            'category' => 'Kubernetes',
            'github_url' => 'https://github.com/tsknitokiana/k8s-multi-cluster-lab',
            'technologies' => ['Kubernetes', 'Istio', 'Prometheus', 'Grafana', 'Helm'],
            'status' => 'in_progress',
            'start_date' => '2024-05-01',
            'is_featured' => false,
            'is_active' => true,
            'order' => 3
        ]);

        Project::create([
            'title' => 'Lab Sécurité DevSecOps',
            'description' => 'Laboratoire de sécurité intégrant les pratiques DevSecOps avec scan de vulnérabilités automatisé, analyse de code statique, et tests de pénétration dans le pipeline CI/CD.',
            'type' => 'lab',
            'category' => 'Security',
            'github_url' => 'https://github.com/tsknitokiana/devsecops-lab',
            'technologies' => ['OWASP ZAP', 'SonarQube', 'Trivy', 'Vault', 'Falco'],
            'status' => 'in_progress',
            'start_date' => '2024-04-10',
            'is_featured' => false,
            'is_active' => true,
            'order' => 4
        ]);

        // Démonstrations
        Project::create([
            'title' => 'Démo Monitoring Stack Complète',
            'description' => 'Démonstration d\'une stack de monitoring complète avec Prometheus, Grafana, AlertManager, et Jaeger pour le tracing distribué. Tableaux de bord personnalisés et alertes intelligentes.',
            'type' => 'demo',
            'category' => 'Monitoring',
            'demo_url' => 'https://monitoring-demo.tsikyportfolio.com',
            'github_url' => 'https://github.com/tsknitokiana/monitoring-stack-demo',
            'technologies' => ['Prometheus', 'Grafana', 'AlertManager', 'Jaeger', 'Node Exporter'],
            'status' => 'completed',
            'start_date' => '2024-03-01',
            'end_date' => '2024-03-25',
            'is_featured' => true,
            'is_active' => true,
            'order' => 5
        ]);

        Project::create([
            'title' => 'Démo GitOps avec ArgoCD',
            'description' => 'Démonstration des pratiques GitOps avec ArgoCD pour le déploiement automatisé sur Kubernetes. Synchronisation automatique, rollback, et gestion des environnements multiples.',
            'type' => 'demo',
            'category' => 'GitOps',
            'demo_url' => 'https://argocd-demo.tsikyportfolio.com',
            'github_url' => 'https://github.com/tsknitokiana/gitops-argocd-demo',
            'technologies' => ['ArgoCD', 'Kubernetes', 'Helm', 'Git', 'Kustomize'],
            'status' => 'completed',
            'start_date' => '2024-04-01',
            'end_date' => '2024-04-20',
            'is_featured' => false,
            'is_active' => true,
            'order' => 6
        ]);

        // Projet Cloud
        Project::create([
            'title' => 'Migration Cloud Multi-Provider',
            'description' => 'Projet de migration d\'infrastructure on-premise vers le cloud avec stratégie multi-provider (AWS, Azure, GCP). Optimisation des coûts et haute disponibilité.',
            'type' => 'project',
            'category' => 'Cloud',
            'project_url' => 'https://cloud-migration.tsikyportfolio.com',
            'github_url' => 'https://github.com/tsknitokiana/cloud-migration-project',
            'technologies' => ['AWS', 'Azure', 'GCP', 'Terraform', 'Ansible'],
            'status' => 'planned',
            'start_date' => '2024-07-01',
            'is_featured' => false,
            'is_active' => true,
            'order' => 7
        ]);
    }
}
