<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->string('type')->default('project'); // project, lab, demo
            $table->string('category')->nullable(); // web, mobile, devops, ai, etc.
            $table->string('project_url')->nullable(); // Lien vers le projet
            $table->string('github_url')->nullable(); // Lien GitHub
            $table->string('demo_url')->nullable(); // Lien démo
            $table->string('image')->nullable(); // Image du projet
            $table->json('technologies')->nullable(); // Technologies utilisées
            $table->string('status')->default('completed'); // completed, in_progress, planned
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->boolean('is_featured')->default(false); // Projet mis en avant
            $table->boolean('is_active')->default(true);
            $table->integer('order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
