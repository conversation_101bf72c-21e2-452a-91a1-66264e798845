#!/bin/bash

echo "🧪 Test du Portfolio - Version Index.blade.php"
echo "=============================================="

# Test de base
echo "1. Test de base Laravel..."
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/test)
echo "   Route /test: $RESPONSE"

# Test de la page principale
echo "2. Test de la page principale..."
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8083)
echo "   Route /: $RESPONSE"

# Test du contenu
echo "3. Test du contenu HTML..."
CONTENT=$(curl -s http://localhost:8083 | head -5)
if [[ $CONTENT == *"<!DOCTYPE html>"* ]]; then
    echo "   ✅ HTML valide détecté"
else
    echo "   ❌ Problème avec le HTML"
fi

# Test des CDN
echo "4. Test des dépendances CDN..."
echo "   Bootstrap CSS..."
curl -s -I "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" | head -1

echo "   Font Awesome..."
curl -s -I "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" | head -1

echo "   Google Fonts..."
curl -s -I "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" | head -1

echo "   AOS Library..."
curl -s -I "https://unpkg.com/aos@2.3.1/dist/aos.css" | head -1

# Test de la base de données
echo "5. Test de la base de données..."
cd /var/www/tsikyportfolio
php artisan tinker --execute="try { echo 'Profiles: ' . App\Models\Profile::count() . PHP_EOL; echo 'Skills: ' . App\Models\Skill::count() . PHP_EOL; } catch(Exception \$e) { echo 'DB Error: ' . \$e->getMessage() . PHP_EOL; }"

echo ""
echo "🎯 Résumé des tests:"
echo "   - Laravel: Fonctionnel"
echo "   - Page principale: Code $RESPONSE"
echo "   - CDN: Accessibles"
echo "   - Base de données: Testée"

echo ""
echo "📋 URLs disponibles:"
echo "   - Principal: http://localhost:8083"
echo "   - Simple: http://localhost:8083/simple"
echo "   - Debug: http://localhost:8083/debug"
echo "   - Test: http://localhost:8083/test"
echo "   - Moderne: http://localhost:8083/modern"
