<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class TranslationService
{
    /**
     * Traduit un texte vers la langue cible
     */
    public function translate(string $text, string $targetLanguage, string $sourceLanguage = 'auto'): string
    {
        // Si le texte est vide, retourner tel quel
        if (empty(trim($text))) {
            return $text;
        }

        // Si la langue cible est la même que la source, pas besoin de traduire
        if ($sourceLanguage === $targetLanguage) {
            return $text;
        }

        // Créer une clé de cache unique
        $cacheKey = 'translation_' . md5($text . $sourceLanguage . $targetLanguage);

        // Vérifier le cache d'abord
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Utiliser la traduction de fallback
            $translation = $this->fallbackTranslation($text, $targetLanguage);

            // Mettre en cache pour 24 heures
            Cache::put($cacheKey, $translation, 60 * 24);

            return $translation;

        } catch (\Exception $e) {
            Log::error('Translation error: ' . $e->getMessage());
            return $text; // Retourner le texte original en cas d'erreur
        }
    }

    /**
     * Traduction de fallback (règles simples)
     */
    private function fallbackTranslation(string $text, string $targetLanguage): string
    {
        // Dictionnaire de traductions courantes
        $translations = [
            'fr' => [
                // Titres de postes
                'DevOps Engineer' => 'Ingénieur DevOps',
                'Cloud Architect' => 'Architecte Cloud',
                'Software Developer' => 'Développeur Logiciel',
                'System Administrator' => 'Administrateur Système',
                'Full Stack Developer' => 'Développeur Full Stack',
                'Backend Developer' => 'Développeur Backend',
                'Frontend Developer' => 'Développeur Frontend',
                'Senior Developer' => 'Développeur Senior',
                'Lead Developer' => 'Développeur Principal',
                'Technical Lead' => 'Responsable Technique',
                
                // Compétences techniques
                'Programming' => 'Programmation',
                'Database Management' => 'Gestion de Base de Données',
                'Cloud Computing' => 'Informatique en Nuage',
                'Machine Learning' => 'Apprentissage Automatique',
                'Artificial Intelligence' => 'Intelligence Artificielle',
                'Web Development' => 'Développement Web',
                'Mobile Development' => 'Développement Mobile',
                'Software Engineering' => 'Génie Logiciel',
                'Data Science' => 'Science des Données',
                'Cybersecurity' => 'Cybersécurité',
                
                // Technologies
                'JavaScript' => 'JavaScript',
                'Python' => 'Python',
                'Java' => 'Java',
                'PHP' => 'PHP',
                'React' => 'React',
                'Vue.js' => 'Vue.js',
                'Angular' => 'Angular',
                'Node.js' => 'Node.js',
                'Laravel' => 'Laravel',
                'Docker' => 'Docker',
                'Kubernetes' => 'Kubernetes',
                'AWS' => 'AWS',
                'Azure' => 'Azure',
                'Google Cloud' => 'Google Cloud',
                
                // Formations
                'Bachelor of Science' => 'Licence en Sciences',
                'Master of Science' => 'Master en Sciences',
                'Computer Science' => 'Informatique',
                'Information Technology' => 'Technologies de l\'Information',
                'Software Engineering' => 'Génie Logiciel',
                'Computer Engineering' => 'Génie Informatique',
                'Data Science' => 'Science des Données',
                
                // Mots courants
                'Present' => 'Présent',
                'Current' => 'Actuel',
                'Ongoing' => 'En cours',
                'Completed' => 'Terminé',
                'In Progress' => 'En cours',
                'Planned' => 'Planifié',
                'Experience' => 'Expérience',
                'Education' => 'Formation',
                'Skills' => 'Compétences',
                'Projects' => 'Projets',
                'Certifications' => 'Certifications',
                'Company' => 'Entreprise',
                'University' => 'Université',
                'Institute' => 'Institut',
                'School' => 'École',
                
                // Phrases courantes
                'Responsible for' => 'Responsable de',
                'Developed and maintained' => 'Développé et maintenu',
                'Collaborated with' => 'Collaboré avec',
                'Implemented' => 'Implémenté',
                'Designed' => 'Conçu',
                'Managed' => 'Géré',
                'Led' => 'Dirigé',
                'Created' => 'Créé',
                'Optimized' => 'Optimisé',
                'Deployed' => 'Déployé',
                'Maintained' => 'Maintenu',
                'Developed' => 'Développé',
                'Built' => 'Construit',
                'Architected' => 'Architecturé',
                'Integrated' => 'Intégré',
                'Automated' => 'Automatisé',
                'Monitored' => 'Surveillé',
                'Troubleshot' => 'Dépanné',
                'Configured' => 'Configuré',
                'Administered' => 'Administré',
            ],
            'en' => [
                // Traductions inverses (français vers anglais)
                'Ingénieur DevOps' => 'DevOps Engineer',
                'Architecte Cloud' => 'Cloud Architect',
                'Développeur Logiciel' => 'Software Developer',
                'Administrateur Système' => 'System Administrator',
                'Développeur Full Stack' => 'Full Stack Developer',
                'Développeur Backend' => 'Backend Developer',
                'Développeur Frontend' => 'Frontend Developer',
                'Développeur Senior' => 'Senior Developer',
                'Développeur Principal' => 'Lead Developer',
                'Responsable Technique' => 'Technical Lead',
                
                'Programmation' => 'Programming',
                'Gestion de Base de Données' => 'Database Management',
                'Informatique en Nuage' => 'Cloud Computing',
                'Apprentissage Automatique' => 'Machine Learning',
                'Intelligence Artificielle' => 'Artificial Intelligence',
                'Développement Web' => 'Web Development',
                'Développement Mobile' => 'Mobile Development',
                'Génie Logiciel' => 'Software Engineering',
                'Science des Données' => 'Data Science',
                'Cybersécurité' => 'Cybersecurity',
                
                'Licence en Sciences' => 'Bachelor of Science',
                'Master en Sciences' => 'Master of Science',
                'Informatique' => 'Computer Science',
                'Technologies de l\'Information' => 'Information Technology',
                'Génie Informatique' => 'Computer Engineering',
                
                'Présent' => 'Present',
                'Actuel' => 'Current',
                'En cours' => 'In Progress',
                'Terminé' => 'Completed',
                'Planifié' => 'Planned',
                'Expérience' => 'Experience',
                'Formation' => 'Education',
                'Compétences' => 'Skills',
                'Projets' => 'Projects',
                'Certifications' => 'Certifications',
                'Entreprise' => 'Company',
                'Université' => 'University',
                'Institut' => 'Institute',
                'École' => 'School',
                
                'Responsable de' => 'Responsible for',
                'Développé et maintenu' => 'Developed and maintained',
                'Collaboré avec' => 'Collaborated with',
                'Implémenté' => 'Implemented',
                'Conçu' => 'Designed',
                'Géré' => 'Managed',
                'Dirigé' => 'Led',
                'Créé' => 'Created',
                'Optimisé' => 'Optimized',
                'Déployé' => 'Deployed',
                'Maintenu' => 'Maintained',
                'Développé' => 'Developed',
                'Construit' => 'Built',
                'Architecturé' => 'Architected',
                'Intégré' => 'Integrated',
                'Automatisé' => 'Automated',
                'Surveillé' => 'Monitored',
                'Dépanné' => 'Troubleshot',
                'Configuré' => 'Configured',
                'Administré' => 'Administered',
            ]
        ];

        // Vérifier les traductions exactes d'abord
        if (isset($translations[$targetLanguage][$text])) {
            return $translations[$targetLanguage][$text];
        }

        // Traduction partielle (remplacer les mots connus)
        $translatedText = $text;
        if (isset($translations[$targetLanguage])) {
            foreach ($translations[$targetLanguage] as $original => $translation) {
                $translatedText = str_ireplace($original, $translation, $translatedText);
            }
        }

        return $translatedText;
    }
}
