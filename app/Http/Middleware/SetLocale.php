<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Routes à exclure du middleware de langue
        $excludedRoutes = [
            'contact.send',
            'admin.*'
        ];

        // Vérifier si la route actuelle doit être exclue
        $currentRoute = $request->route();
        if ($currentRoute) {
            $routeName = $currentRoute->getName();
            foreach ($excludedRoutes as $excluded) {
                if ($routeName && (str_contains($excluded, '*') ? str_starts_with($routeName, str_replace('*', '', $excluded)) : $routeName === $excluded)) {
                    // Pour les routes exclues, utiliser la langue de session ou défaut
                    $locale = Session::get('locale', 'en');
                    App::setLocale($locale);
                    return $next($request);
                }
            }
        }

        // Langues supportées
        $supportedLocales = ['en', 'fr'];
        $defaultLocale = 'en';
        $locale = $defaultLocale;

        try {
            // 1. Vérifier si une langue est définie manuellement dans la session
            if (Session::has('locale') && in_array(Session::get('locale'), $supportedLocales)) {
                $locale = Session::get('locale');
            }
            // 2. Vérifier si une langue est passée en paramètre
            elseif ($request->has('lang') && in_array($request->get('lang'), $supportedLocales)) {
                $locale = $request->get('lang');
                Session::put('locale', $locale);
            }
            // 3. Détecter selon l'en-tête Accept-Language du navigateur
            else {
                $locale = $this->detectLocaleFromBrowser($request, $supportedLocales, $defaultLocale);
                Session::put('locale', $locale);
            }
        } catch (\Exception $e) {
            // En cas d'erreur, utiliser la langue par défaut
            $locale = $defaultLocale;
        }

        App::setLocale($locale);

        return $next($request);
    }

    /**
     * Détecter la langue selon le navigateur et la géolocalisation
     */
    private function detectLocaleFromBrowser(Request $request, array $supportedLocales, string $defaultLocale): string
    {
        try {
            // Vérification géographique pour Madagascar en premier
            if ($this->isFromMadagascar($request)) {
                return 'fr';
            }

            // Récupérer les langues préférées du navigateur
            $acceptLanguage = $request->header('Accept-Language');

            if (!$acceptLanguage) {
                return $defaultLocale;
            }

            // Parser l'en-tête Accept-Language de manière simple
            $languages = explode(',', $acceptLanguage);

            foreach ($languages as $lang) {
                $langCode = strtolower(substr(trim($lang), 0, 2));

                if (in_array($langCode, $supportedLocales)) {
                    return $langCode;
                }
            }

            return $defaultLocale;
        } catch (\Exception $e) {
            return $defaultLocale;
        }
    }

    /**
     * Détecter si la requête vient de Madagascar
     */
    private function isFromMadagascar(Request $request): bool
    {
        try {
            // 1. Vérifier l'en-tête Accept-Language pour les codes de Madagascar
            $acceptLanguage = $request->header('Accept-Language', '');
            if (stripos($acceptLanguage, 'mg') !== false) {
                return true;
            }

            // 2. Vérifier l'IP pour les plages communes de Madagascar
            $ip = $request->ip();
            $madagascarIpRanges = [
                '196.192.', // Telma
                '41.188.',  // Orange Madagascar
                '154.126.', // Airtel Madagascar
            ];

            foreach ($madagascarIpRanges as $range) {
                if (strpos($ip, $range) === 0) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
