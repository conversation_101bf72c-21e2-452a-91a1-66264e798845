<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactAutoReply;
use App\Mail\ContactNotification;

class ContactController extends Controller
{
    public function send(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        try {
            // Envoyer l'email de notification à l'admin
            Mail::to('<EMAIL>')->send(new ContactNotification($validated));

            // Envoyer l'email de réponse automatique au visiteur
            Mail::to($validated['email'])->send(new ContactAutoReply($validated));

            return response()->json([
                'success' => true,
                'message' => 'Votre message a été envoyé avec succès ! Vous recevrez une réponse sous peu.'
            ]);

        } catch (\Exception $e) {
    dump($e);
    return response()->json([
        'success' => false,
        'message' => 'Une erreur est survenue lors de l\'envoi.',
        'error' => $e->getMessage()
    ], 500);
}

    }
}
