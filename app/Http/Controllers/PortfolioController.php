<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Profile;
use App\Models\Experience;
use App\Models\Skill;
use App\Models\Education;
use App\Models\Certification;
use App\Models\Video;
use App\Models\Project;
use App\Services\TranslationService;

class PortfolioController extends Controller
{
    /**
     * Display the portfolio homepage
     */
    public function index()
    {
        $profile = Profile::first();
        $experiences = Experience::ordered()->get();
        $skills = Skill::ordered()->get();
        $educations = Education::ordered()->get();
        $certifications = Certification::ordered()->get();
        $projects = Project::active()->ordered()->get();
        $presentationVideo = Video::where('is_presentation', true)->where('is_active', true)->first();

        // Appliquer les traductions automatiques selon la langue actuelle
        $currentLocale = app()->getLocale();
        if ($currentLocale !== 'en') {
            $translationService = app(TranslationService::class);

            // Traduire les expériences
            $experiences = $experiences->map(function ($experience) {
                return $experience->translateFields();
            });

            // Traduire les compétences
            $skills = $skills->map(function ($skill) {
                return $skill->translateFields();
            });

            // Traduire les formations
            $educations = $educations->map(function ($education) {
                return $education->translateFields();
            });

            // Traduire les certifications
            $certifications = $certifications->map(function ($certification) {
                return $certification->translateFields();
            });

            // Traduire les projets
            $projects = $projects->map(function ($project) {
                return $project->translateFields();
            });
        }

        return view('portfolio.index', compact('profile', 'experiences', 'skills', 'educations', 'certifications', 'projects', 'presentationVideo'));
    }

    /**
     * Display simple portfolio version
     */
    public function simple()
    {
        try {
            $profile = Profile::first();
            $experiences = Experience::ordered()->get();
            $skills = Skill::ordered()->get();
            $educations = Education::ordered()->get();
            $certifications = Certification::ordered()->get();

            return view('portfolio.simple', compact('profile', 'experiences', 'skills', 'educations', 'certifications'));
        } catch (\Exception $e) {
            // Ultimate fallback
            return response()->view('portfolio.emergency', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Display modern standalone portfolio version
     */
    public function modernStandalone()
    {
        try {
            $profile = Profile::first();
            $experiences = Experience::ordered()->get();
            $skills = Skill::ordered()->get();
            $educations = Education::ordered()->get();
            $certifications = Certification::ordered()->get();

            return view('portfolio.modern-standalone', compact('profile', 'experiences', 'skills', 'educations', 'certifications'));
        } catch (\Exception $e) {
            return response()->view('portfolio.error-debug', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Debug information
     */
    public function debug()
    {
        $debug = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'environment' => app()->environment(),
            'database_connection' => 'Unknown',
            'tables' => [],
            'profile_count' => 0,
            'experience_count' => 0,
            'skill_count' => 0,
            'education_count' => 0,
            'certification_count' => 0,
        ];

        try {
            \DB::connection()->getPdo();
            $debug['database_connection'] = 'Connected';

            $debug['tables'] = \DB::select('SHOW TABLES');
            $debug['profile_count'] = Profile::count();
            $debug['experience_count'] = Experience::count();
            $debug['skill_count'] = Skill::count();
            $debug['education_count'] = Education::count();
            $debug['certification_count'] = Certification::count();
        } catch (\Exception $e) {
            $debug['database_error'] = $e->getMessage();
        }

        return response()->json($debug);
    }
}
