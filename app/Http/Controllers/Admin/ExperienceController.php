<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Experience;

class ExperienceController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $experiences = Experience::ordered()->get();
        return view('admin.experiences.index', compact('experiences'));
    }

    public function create()
    {
        return view('admin.experiences.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'position' => 'required|string|max:255',
            'company' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'current' => 'boolean',
            'description' => 'required|string',
            'responsibilities' => 'nullable|array',
            'employment_type' => 'required|string|in:full-time,part-time,contract,freelance,internship',
            'order' => 'nullable|integer|min:0',
        ]);

        $validated['current'] = $request->has('current');
        $validated['responsibilities'] = $request->input('responsibilities', []);
        $validated['order'] = $validated['order'] ?? 0;

        if ($validated['current']) {
            $validated['end_date'] = null;
        }

        Experience::create($validated);

        return redirect()->route('admin.experiences.index')
            ->with('success', 'Experience created successfully.');
    }

    public function show(Experience $experience)
    {
        return view('admin.experiences.show', compact('experience'));
    }

    public function edit(Experience $experience)
    {
        return view('admin.experiences.edit', compact('experience'));
    }

    public function update(Request $request, Experience $experience)
    {
        $validated = $request->validate([
            'position' => 'required|string|max:255',
            'company' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'current' => 'boolean',
            'description' => 'required|string',
            'responsibilities' => 'nullable|array',
            'employment_type' => 'required|string|in:full-time,part-time,contract,freelance,internship',
            'order' => 'nullable|integer|min:0',
        ]);

        $validated['current'] = $request->has('current');
        $validated['responsibilities'] = $request->input('responsibilities', []);
        $validated['order'] = $validated['order'] ?? 0;

        if ($validated['current']) {
            $validated['end_date'] = null;
        }

        $experience->update($validated);

        return redirect()->route('admin.experiences.index')
            ->with('success', 'Experience updated successfully.');
    }

    public function destroy(Experience $experience)
    {
        $experience->delete();

        return redirect()->route('admin.experiences.index')
            ->with('success', 'Experience deleted successfully.');
    }
}
