<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Video;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class VideoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $videos = Video::orderBy('order')->orderBy('created_at', 'desc')->get();
        return view('admin.videos.index', compact('videos'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.videos.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'video_type' => 'required|in:youtube,vimeo,upload',
            'video_url' => 'nullable|url|required_if:video_type,youtube,vimeo',
            'video_file' => 'nullable|file|mimes:mp4,avi,mov,wmv|max:102400|required_if:video_type,upload', // 100MB max
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'is_presentation' => 'boolean',
            'duration' => 'nullable|integer|min:1',
            'order' => 'nullable|integer|min:0',
        ]);

        // Si c'est une vidéo de présentation, désactiver les autres
        if ($request->has('is_presentation') && $request->is_presentation) {
            Video::where('is_presentation', true)->update(['is_presentation' => false]);
        }

        // Gérer l'upload de la vidéo
        if ($request->hasFile('video_file')) {
            $videoPath = $request->file('video_file')->store('videos', 'public');
            $validated['video_file'] = $videoPath;
        }

        // Gérer l'upload du thumbnail
        if ($request->hasFile('thumbnail')) {
            $thumbnailPath = $request->file('thumbnail')->store('videos/thumbnails', 'public');
            $validated['thumbnail'] = $thumbnailPath;
        }

        $validated['is_active'] = $request->has('is_active');
        $validated['is_presentation'] = $request->has('is_presentation');

        Video::create($validated);

        return redirect()->route('admin.videos.index')
            ->with('success', 'Vidéo créée avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Video $video)
    {
        return view('admin.videos.show', compact('video'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Video $video)
    {
        return view('admin.videos.edit', compact('video'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Video $video)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'video_type' => 'required|in:youtube,vimeo,upload',
            'video_url' => 'nullable|url|required_if:video_type,youtube,vimeo',
            'video_file' => 'nullable|file|mimes:mp4,avi,mov,wmv|max:102400',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'is_presentation' => 'boolean',
            'duration' => 'nullable|integer|min:1',
            'order' => 'nullable|integer|min:0',
        ]);

        // Si c'est une vidéo de présentation, désactiver les autres
        if ($request->has('is_presentation') && $request->is_presentation) {
            Video::where('is_presentation', true)->where('id', '!=', $video->id)->update(['is_presentation' => false]);
        }

        // Gérer l'upload de la nouvelle vidéo
        if ($request->hasFile('video_file')) {
            // Supprimer l'ancienne vidéo
            if ($video->video_file && Storage::disk('public')->exists($video->video_file)) {
                Storage::disk('public')->delete($video->video_file);
            }

            $videoPath = $request->file('video_file')->store('videos', 'public');
            $validated['video_file'] = $videoPath;
        }

        // Gérer l'upload du nouveau thumbnail
        if ($request->hasFile('thumbnail')) {
            // Supprimer l'ancien thumbnail s'il existe et qu'il est local
            if ($video->thumbnail && str_starts_with($video->thumbnail, 'videos/') && Storage::disk('public')->exists($video->thumbnail)) {
                Storage::disk('public')->delete($video->thumbnail);
            }

            $thumbnailPath = $request->file('thumbnail')->store('videos/thumbnails', 'public');
            $validated['thumbnail'] = $thumbnailPath;
        }

        $validated['is_active'] = $request->has('is_active');
        $validated['is_presentation'] = $request->has('is_presentation');

        $video->update($validated);

        return redirect()->route('admin.videos.index')
            ->with('success', 'Vidéo mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Video $video)
    {
        // Supprimer les fichiers associés
        if ($video->video_file && Storage::disk('public')->exists($video->video_file)) {
            Storage::disk('public')->delete($video->video_file);
        }

        if ($video->thumbnail && str_starts_with($video->thumbnail, 'videos/') && Storage::disk('public')->exists($video->thumbnail)) {
            Storage::disk('public')->delete($video->thumbnail);
        }

        $video->delete();

        return redirect()->route('admin.videos.index')
            ->with('success', 'Vidéo supprimée avec succès.');
    }
}
