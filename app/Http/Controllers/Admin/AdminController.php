<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Profile;
use App\Models\Experience;
use App\Models\Skill;
use App\Models\Education;
use App\Models\Certification;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        $stats = [
            'profiles' => Profile::count(),
            'experiences' => Experience::count(),
            'skills' => Skill::count(),
            'educations' => Education::count(),
            'certifications' => Certification::count(),
        ];

        $recentExperiences = Experience::latest()->take(3)->get();
        $recentSkills = Skill::latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recentExperiences', 'recentSkills'));
    }
}
