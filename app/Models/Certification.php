<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\Translatable;

class Certification extends Model
{
    use HasFactory, Translatable;

    protected $fillable = [
        'name', 'issuing_organization', 'issue_date', 'expiration_date',
        'credential_id', 'credential_url', 'description', 'icon', 'order'
    ];

    protected $casts = [
        'issue_date' => 'date',
        'expiration_date' => 'date',
    ];

    public function scopeOrdered($query)
    {
        return $query->orderBy('order')->orderBy('issue_date', 'desc');
    }

    public function getDateDisplayAttribute()
    {
        return $this->issue_date->format('Y');
    }

    /**
     * Champs à traduire automatiquement
     */
    protected $translatableFields = [
        'name',
        'issuing_organization',
        'description'
    ];
}
