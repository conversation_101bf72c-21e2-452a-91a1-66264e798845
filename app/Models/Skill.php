<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\Translatable;

class Skill extends Model
{
    use HasFactory, Translatable;

    protected $fillable = [
        'name', 'category', 'level', 'description', 'icon', 'order'
    ];

    public function scopeOrdered($query)
    {
        return $query->orderBy('order')->orderBy('level', 'desc');
    }

    /**
     * Champs à traduire automatiquement
     */
    protected $translatableFields = [
        'name',
        'description'
    ];
}
