<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'title',
        'description',
        'email',
        'phone',
        'address',
        'website',
        'linkedin',
        'github',
        'twitter',
        'avatar',
        'birth_date',
        'cv_url',
        'freelance_available',
        'languages'
    ];

    protected $casts = [
        'birth_date' => 'date',
        'freelance_available' => 'boolean',
        'languages' => 'array'
    ];

    public function getAgeAttribute()
    {
        return $this->birth_date ? $this->birth_date->age : null;
    }

    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            // Si c'est un chemin de fichier local (commence par avatars/)
            if (str_starts_with($this->avatar, 'avatars/')) {
                return asset('storage/' . $this->avatar);
            }
            // Si c'est déjà une URL complète
            if (filter_var($this->avatar, FILTER_VALIDATE_URL)) {
                return $this->avatar;
            }
            // Fallback pour les anciens chemins
            return asset('storage/' . $this->avatar);
        }

        // Avatar par défaut
        return asset('images/default-avatar.svg');
    }
}
