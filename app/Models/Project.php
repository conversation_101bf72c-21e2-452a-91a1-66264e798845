<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use App\Traits\Translatable;

class Project extends Model
{
    use HasFactory, Translatable;

    protected $fillable = [
        'title',
        'description',
        'type',
        'category',
        'project_url',
        'github_url',
        'demo_url',
        'image',
        'technologies',
        'status',
        'start_date',
        'end_date',
        'is_featured',
        'is_active',
        'order'
    ];

    protected $casts = [
        'technologies' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function getImageUrlAttribute()
    {
        if ($this->image) {
            if (str_starts_with($this->image, 'projects/')) {
                return asset('storage/' . $this->image);
            }
            if (filter_var($this->image, FILTER_VALIDATE_URL)) {
                return $this->image;
            }
            return asset('storage/' . $this->image);
        }

        return asset('images/default-project.svg');
    }

    public function getDurationAttribute()
    {
        if (!$this->start_date) return 'N/A';

        $start = $this->start_date;
        $end = $this->end_date ?? now();

        $diff = $start->diffInMonths($end);

        if ($diff < 1) {
            return '< 1 mois';
        } elseif ($diff < 12) {
            return $diff . ' mois';
        } else {
            $years = floor($diff / 12);
            $months = $diff % 12;
            return $years . ' an' . ($years > 1 ? 's' : '') . ($months > 0 ? ' ' . $months . ' mois' : '');
        }
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'completed' => ['class' => 'success', 'text' => 'Terminé'],
            'in_progress' => ['class' => 'primary', 'text' => 'En cours'],
            'planned' => ['class' => 'secondary', 'text' => 'Planifié'],
        ];

        return $badges[$this->status] ?? ['class' => 'secondary', 'text' => 'Inconnu'];
    }

    public function getTypeBadgeAttribute()
    {
        $badges = [
            'project' => ['class' => 'primary', 'text' => 'Projet'],
            'lab' => ['class' => 'warning', 'text' => 'Lab'],
            'demo' => ['class' => 'info', 'text' => 'Démo'],
        ];

        return $badges[$this->type] ?? ['class' => 'secondary', 'text' => 'Autre'];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order')->orderBy('created_at', 'desc');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Champs à traduire automatiquement
     */
    protected $translatableFields = [
        'title',
        'description',
        'category'
    ];
}
