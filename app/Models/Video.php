<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Video extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'video_type',
        'video_url',
        'video_file',
        'thumbnail',
        'is_active',
        'is_presentation',
        'duration',
        'order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_presentation' => 'boolean',
    ];

    public function getEmbedUrlAttribute()
    {
        if ($this->video_type === 'youtube') {
            // Extraire l'ID YouTube de différents formats d'URL
            if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $this->video_url, $matches)) {
                return 'https://www.youtube.com/embed/' . $matches[1] . '?autoplay=1&rel=0&modestbranding=1';
            }
        } elseif ($this->video_type === 'vimeo') {
            // Extraire l'ID Vimeo
            if (preg_match('/vimeo\.com\/(\d+)/', $this->video_url, $matches)) {
                return 'https://player.vimeo.com/video/' . $matches[1] . '?autoplay=1';
            }
        } elseif ($this->video_type === 'upload' && $this->video_file) {
            return asset('storage/' . $this->video_file);
        }

        return $this->video_url;
    }

    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail) {
            if (str_starts_with($this->thumbnail, 'videos/')) {
                return asset('storage/' . $this->thumbnail);
            }
            return $this->thumbnail;
        }

        // Thumbnail par défaut basé sur le type
        if ($this->video_type === 'youtube' && $this->video_url) {
            if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $this->video_url, $matches)) {
                return 'https://img.youtube.com/vi/' . $matches[1] . '/maxresdefault.jpg';
            }
        }

        return asset('images/default-video-thumbnail.svg');
    }

    public function getDurationFormattedAttribute()
    {
        if (!$this->duration) return 'N/A';

        $minutes = floor($this->duration / 60);
        $seconds = $this->duration % 60;

        return sprintf('%d:%02d', $minutes, $seconds);
    }

    // Scope pour la vidéo de présentation active
    public function scopePresentationVideo($query)
    {
        return $query->where('is_presentation', true)->where('is_active', true)->first();
    }
}
