<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use App\Traits\Translatable;

class Experience extends Model
{
    use HasFactory, Translatable;

    protected $fillable = [
        'position',
        'company',
        'location',
        'start_date',
        'end_date',
        'current',
        'description',
        'responsibilities',
        'employment_type',
        'order'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'current' => 'boolean',
        'responsibilities' => 'array'
    ];

    /**
     * Champs à traduire automatiquement
     */
    protected $translatableFields = [
        'position',
        'company',
        'description'
    ];

    public function scopeOrdered($query)
    {
        return $query->orderBy('order')->orderBy('start_date', 'desc');
    }

    public function getDateRangeAttribute()
    {
        $start = $this->start_date->format('M Y');
        $end = $this->current ? 'Present' : ($this->end_date ? $this->end_date->format('M Y') : 'Present');

        return "{$start} - {$end}";
    }
}
