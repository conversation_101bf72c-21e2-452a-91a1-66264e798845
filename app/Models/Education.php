<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\Translatable;

class Education extends Model
{
    use HasFactory, Translatable;

    protected $table = 'education';

    protected $fillable = [
        'degree', 'field_of_study', 'institution', 'location',
        'start_date', 'end_date', 'description', 'grade', 'order'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date'
    ];

    public function scopeOrdered($query)
    {
        return $query->orderBy('order')->orderBy('start_date', 'desc');
    }

    public function getDateRangeAttribute()
    {
        $start = $this->start_date->format('Y');
        $end = $this->end_date ? $this->end_date->format('Y') : 'Present';

        return $start === $end ? $start : "{$start} - {$end}";
    }

    /**
     * Champs à traduire automatiquement
     */
    protected $translatableFields = [
        'degree',
        'institution',
        'description'
    ];
}
