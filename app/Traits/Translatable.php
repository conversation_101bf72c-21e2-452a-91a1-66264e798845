<?php

namespace App\Traits;

use App\Services\TranslationService;
use Illuminate\Support\Facades\App;

trait Translatable
{
    /**
     * Obtient la traduction d'un champ
     */
    public function getTranslatedAttribute($field)
    {
        $currentLocale = App::getLocale();
        $originalValue = $this->getAttribute($field);

        // Si c'est déjà la langue par défaut (anglais), retourner tel quel
        if ($currentLocale === 'en') {
            return $originalValue;
        }

        // Si le champ est vide, retourner tel quel
        if (empty($originalValue)) {
            return $originalValue;
        }

        // Utiliser le service de traduction
        $translationService = app(TranslationService::class);
        return $translationService->translate($originalValue, $currentLocale);
    }

    /**
     * Traduit tous les champs traduisibles
     */
    public function translateFields()
    {
        $currentLocale = App::getLocale();
        
        // Si c'est déjà en anglais, pas besoin de traduire
        if ($currentLocale === 'en') {
            return $this;
        }

        $translationService = app(TranslationService::class);
        
        foreach ($this->getTranslatableFields() as $field) {
            if ($this->getAttribute($field)) {
                $translatedValue = $translationService->translate(
                    $this->getAttribute($field), 
                    $currentLocale
                );
                $this->setAttribute($field, $translatedValue);
            }
        }

        return $this;
    }

    /**
     * Obtient les champs traduisibles
     */
    public function getTranslatableFields()
    {
        return property_exists($this, 'translatableFields') ? $this->translatableFields : [];
    }

    /**
     * Définit les champs traduisibles
     */
    public function setTranslatableFields(array $fields)
    {
        $this->translatableFields = $fields;
        return $this;
    }
}
