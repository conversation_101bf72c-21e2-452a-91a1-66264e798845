# 🚀 Guide de Déploiement - Portfolio Tsiky

## 🔧 Problème Identifié
Le site https://tsiky-nitokiana.webs.vc affiche une page blanche. Voici les solutions :

## 📋 Checklist de Débogage

### 1. **Vérification des Logs**
```bash
# Vérifier les logs Apache
tail -f /var/log/apache2/error.log

# Vérifier les logs Laravel
tail -f storage/logs/laravel.log
```

### 2. **Vérification des Permissions**
```bash
# Permissions des dossiers
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
chown -R www-data:www-data storage/
chown -R www-data:www-data bootstrap/cache/
```

### 3. **Vérification de la Configuration**
```bash
# Vérifier la configuration
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear
```

### 4. **Vérification de la Base de Données**
```bash
# Tester la connexion
php artisan tinker
>>> DB::connection()->getPdo();
>>> App\Models\Profile::count();
```

## 🛠️ Solutions Rapides

### **Solution 1: Mode Debug**
Accéder à : `https://tsiky-nitokiana.webs.vc/debug`
- Affiche les informations de diagnostic
- Vérifie la connexion DB
- Montre les erreurs

### **Solution 2: Mode Simple**
Accéder à : `https://tsiky-nitokiana.webs.vc/simple`
- Version simplifiée sans dépendances externes
- CSS inline, pas de CDN
- Fonctionne même avec des erreurs

### **Solution 3: Mode d'Urgence**
Si tout échoue, le système bascule automatiquement vers une page d'urgence

## 📁 Fichiers à Déployer

### **Fichiers Critiques:**
1. `routes/web.php` - Routes avec fallbacks
2. `app/Http/Controllers/PortfolioController.php` - Contrôleur avec gestion d'erreurs
3. `resources/views/portfolio/simple.blade.php` - Vue simplifiée
4. `resources/views/portfolio/emergency.blade.php` - Vue d'urgence

### **Configuration .env:**
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://tsiky-nitokiana.webs.vc

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=portfolio
DB_USERNAME=tsk
DB_PASSWORD=tsk2025*
```

## 🔍 Diagnostic des Erreurs Communes

### **Erreur 1: Page Blanche**
**Causes possibles:**
- Erreur PHP fatale
- Problème de permissions
- Erreur de base de données
- Dépendances manquantes

**Solutions:**
1. Activer les logs d'erreur PHP
2. Vérifier les permissions
3. Tester la connexion DB
4. Utiliser la route `/simple`

### **Erreur 2: Erreur 500**
**Causes possibles:**
- Configuration Apache incorrecte
- Fichier .htaccess manquant
- Permissions insuffisantes

**Solutions:**
1. Vérifier le .htaccess
2. Configurer Apache correctement
3. Ajuster les permissions

### **Erreur 3: Assets non chargés**
**Causes possibles:**
- CDN bloqué
- Chemins incorrects
- HTTPS/HTTP mixte

**Solutions:**
1. Utiliser la version simple (CSS inline)
2. Vérifier les chemins des assets
3. Forcer HTTPS

## 🚀 Commandes de Déploiement

### **Déploiement Rapide:**
```bash
# 1. Sauvegarder l'ancien code
cp -r /var/www/html /var/www/html_backup

# 2. Copier les nouveaux fichiers
# (Copier les fichiers depuis le développement)

# 3. Configurer les permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
chown -R www-data:www-data storage/
chown -R www-data:www-data bootstrap/cache/

# 4. Nettoyer le cache
php artisan config:clear
php artisan cache:clear
php artisan view:clear

# 5. Tester
curl -I https://tsiky-nitokiana.webs.vc
curl -I https://tsiky-nitokiana.webs.vc/simple
curl -I https://tsiky-nitokiana.webs.vc/debug
```

## 📞 Support d'Urgence

### **Si le problème persiste:**

1. **Accéder au mode simple:**
   - URL: `https://tsiky-nitokiana.webs.vc/simple`
   - Fonctionne sans dépendances externes

2. **Vérifier le diagnostic:**
   - URL: `https://tsiky-nitokiana.webs.vc/debug`
   - Affiche l'état du système

3. **Contact direct:**
   - Email: <EMAIL>
   - Le portfolio d'urgence affiche les informations de contact

## 🎯 Résultat Attendu

Après le déploiement, le site devrait :
- ✅ Afficher le portfolio moderne sur `/`
- ✅ Avoir un fallback simple sur `/simple`
- ✅ Fournir des infos de debug sur `/debug`
- ✅ Basculer automatiquement en cas d'erreur

## 📈 Monitoring

### **Vérifications Régulières:**
```bash
# Test automatique
curl -s -o /dev/null -w "%{http_code}" https://tsiky-nitokiana.webs.vc
curl -s -o /dev/null -w "%{http_code}" https://tsiky-nitokiana.webs.vc/simple
```

### **Logs à Surveiller:**
- `/var/log/apache2/error.log`
- `storage/logs/laravel.log`
- Logs de base de données

---

**Note:** Ce guide assure que le portfolio reste accessible même en cas de problème technique.
